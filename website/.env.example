# Environment Variables Example
# Copy this file to .env.local and fill in your values

# Site Configuration
NEXT_PUBLIC_SITE_URL=https://augment-app.com
NEXT_PUBLIC_SITE_NAME=Augment

# Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# Contact Form (if using a service like Formspree)
NEXT_PUBLIC_FORM_ENDPOINT=https://formspree.io/f/your-form-id

# Email Configuration (for contact forms)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Database (if needed for contact forms, etc.)
DATABASE_URL=your-database-url

# API Keys (if integrating with external services)
GITHUB_TOKEN=your-github-token
TWITTER_API_KEY=your-twitter-api-key

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_CONTACT_FORM=true
NEXT_PUBLIC_ENABLE_NEWSLETTER=false
