import Foundation
import SwiftUI

/// Comprehensive error recovery framework for Augment application
/// Handles error detection, recovery strategies, and user guidance
public class ErrorRecoveryManager: ObservableObject {

    // MARK: - Singleton
    public static let shared = ErrorRecoveryManager()

    // MARK: - Published Properties
    @Published public var activeErrors: [RecoverableError] = []
    @Published public var isShowingErrorDialog = false
    @Published public var currentError: RecoverableError?
    @Published public var recoveryInProgress = false

    // MARK: - Dependencies
    private let logger = AugmentLogger.shared
    private let notificationManager = NotificationManager.shared
    private let userDefaults = UserDefaults.standard

    // MARK: - Recovery State
    private var recoveryAttempts: [String: Int] = [:]
    private var errorHistory: [ErrorHistoryEntry] = []
    private let maxRecoveryAttempts = 3

    // MARK: - Initialization

    private init() {
        loadErrorHistory()
        setupErrorMonitoring()
        logger.info("Error recovery manager initialized", category: .errorRecovery)
    }

    // MARK: - Error Handling

    /// Reports an error and attempts automatic recovery
    /// - Parameters:
    ///   - error: The error that occurred
    ///   - context: Additional context about where the error occurred
    ///   - autoRecover: Whether to attempt automatic recovery
    public func handleError(_ error: Error, context: String? = nil, autoRecover: Bool = true) {
        let recoverableError = createRecoverableError(from: error, context: context)

        // Log the error
        logger.error("Error occurred: \(error.localizedDescription)", category: .errorRecovery)

        // Add to active errors
        DispatchQueue.main.async {
            self.activeErrors.append(recoverableError)
        }

        // Record in history
        recordError(recoverableError)

        // Attempt automatic recovery if enabled
        if autoRecover {
            attemptAutomaticRecovery(for: recoverableError)
        } else {
            // Show error dialog for manual intervention
            DispatchQueue.main.async {
                self.showErrorDialog(for: recoverableError)
            }
        }
    }

    /// Creates a recoverable error from a standard error
    /// - Parameters:
    ///   - error: The original error
    ///   - context: Additional context
    /// - Returns: A recoverable error with recovery strategies
    private func createRecoverableError(from error: Error, context: String?) -> RecoverableError {
        let errorId = UUID().uuidString
        let timestamp = Date()

        // Determine error category and recovery strategies
        let category = categorizeError(error)
        let strategies = getRecoveryStrategies(for: category, error: error)

        return RecoverableError(
            id: errorId,
            originalError: error,
            category: category,
            context: context,
            timestamp: timestamp,
            recoveryStrategies: strategies,
            userMessage: generateUserMessage(for: error, category: category),
            technicalDetails: generateTechnicalDetails(for: error)
        )
    }

    /// Categorizes an error to determine appropriate recovery strategies
    /// - Parameter error: The error to categorize
    /// - Returns: The error category
    private func categorizeError(_ error: Error) -> ErrorCategory {
        switch error {
        case is FileSystemError:
            return .fileSystem
        case is StorageError:
            return .storage
        case is PermissionError:
            return .permissions
        case is NetworkError:
            return .network
        case is ConfigurationError:
            return .configuration
        default:
            if error.localizedDescription.contains("permission") {
                return .permissions
            } else if error.localizedDescription.contains("space")
                || error.localizedDescription.contains("disk")
            {
                return .storage
            } else if error.localizedDescription.contains("file") {
                return .fileSystem
            } else {
                return .unknown
            }
        }
    }

    /// Gets appropriate recovery strategies for an error category
    /// - Parameters:
    ///   - category: The error category
    ///   - error: The original error
    /// - Returns: Array of recovery strategies
    private func getRecoveryStrategies(for category: ErrorCategory, error: Error)
        -> [RecoveryStrategy]
    {
        switch category {
        case .fileSystem:
            return [
                .retryOperation,
                .checkFilePermissions,
                .recreateFile,
                .contactSupport,
            ]
        case .storage:
            return [
                .freeUpSpace,
                .adjustStorageSettings,
                .runCleanup,
                .contactSupport,
            ]
        case .permissions:
            return [
                .requestPermissions,
                .checkSystemSettings,
                .runAsAdministrator,
                .contactSupport,
            ]
        case .network:
            return [
                .retryOperation,
                .checkNetworkConnection,
                .workOffline,
                .contactSupport,
            ]
        case .configuration:
            return [
                .resetConfiguration,
                .restoreDefaults,
                .reimportSettings,
                .contactSupport,
            ]
        case .unknown:
            return [
                .retryOperation,
                .restartApplication,
                .contactSupport,
            ]
        }
    }

    // MARK: - Automatic Recovery

    /// Attempts automatic recovery for an error
    /// - Parameter error: The recoverable error
    private func attemptAutomaticRecovery(for error: RecoverableError) {
        let attemptCount = recoveryAttempts[error.id] ?? 0

        // Check if we've exceeded max attempts
        guard attemptCount < maxRecoveryAttempts else {
            logger.warning(
                "Max recovery attempts exceeded for error: \(error.id)", category: .errorRecovery)
            DispatchQueue.main.async {
                self.showErrorDialog(for: error)
            }
            return
        }

        // Increment attempt count
        recoveryAttempts[error.id] = attemptCount + 1

        // Try the first automatic recovery strategy
        if let strategy = error.recoveryStrategies.first(where: { $0.isAutomatic }) {
            executeRecoveryStrategy(strategy, for: error)
        } else {
            // No automatic strategies available, show dialog
            DispatchQueue.main.async {
                self.showErrorDialog(for: error)
            }
        }
    }

    /// Executes a specific recovery strategy
    /// - Parameters:
    ///   - strategy: The recovery strategy to execute
    ///   - error: The error being recovered from
    private func executeRecoveryStrategy(_ strategy: RecoveryStrategy, for error: RecoverableError)
    {
        DispatchQueue.main.async {
            self.recoveryInProgress = true
        }

        logger.info(
            "Executing recovery strategy: \(strategy) for error: \(error.id)",
            category: .errorRecovery)

        DispatchQueue.global(qos: .userInitiated).async {
            let success = self.performRecoveryAction(strategy, for: error)

            DispatchQueue.main.async {
                self.recoveryInProgress = false

                if success {
                    self.markErrorAsRecovered(error)
                    self.logger.info(
                        "Recovery successful for error: \(error.id)", category: .errorRecovery)
                } else {
                    // Try next strategy or show dialog
                    self.handleRecoveryFailure(for: error)
                }
            }
        }
    }

    /// Performs the actual recovery action for a strategy
    /// - Parameters:
    ///   - strategy: The recovery strategy
    ///   - error: The error being recovered from
    /// - Returns: Whether the recovery was successful
    private func performRecoveryAction(_ strategy: RecoveryStrategy, for error: RecoverableError)
        -> Bool
    {
        switch strategy {
        case .retryOperation:
            return retryFailedOperation(for: error)
        case .freeUpSpace:
            return attemptSpaceCleanup()
        case .checkFilePermissions:
            return checkAndFixFilePermissions(for: error)
        case .requestPermissions:
            return requestSystemPermissions()
        case .runCleanup:
            return runAutomaticCleanup()
        case .resetConfiguration:
            return resetToDefaultConfiguration()
        case .restoreDefaults:
            return restoreDefaultSettings()
        case .checkNetworkConnection:
            return checkNetworkConnectivity()
        default:
            return false  // Manual strategies return false
        }
    }

    // MARK: - Recovery Actions

    private func retryFailedOperation(for error: RecoverableError) -> Bool {
        // Implementation would depend on the specific operation that failed
        // For now, simulate a retry with 70% success rate
        return Double.random(in: 0...1) > 0.3
    }

    private func attemptSpaceCleanup() -> Bool {
        let storageManager = StorageManager.shared

        // Try to free up space by running cleanup
        let fileSystem = DependencyContainer.shared.augmentFileSystem()
        let spaces = fileSystem.getSpaces()

        var totalFreed: Int64 = 0
        for space in spaces {
            let result = storageManager.cleanupOldVersions(
                olderThan: TimeInterval(30 * 24 * 3600), in: space)  // 30 days
            totalFreed += result.freedBytes
        }

        return totalFreed > 0
    }

    private func checkAndFixFilePermissions(for error: RecoverableError) -> Bool {
        // Check if we can read/write to the file or directory
        guard let context = error.context,
            let url = URL(string: context)
        else {
            return false
        }

        let fileManager = FileManager.default
        return fileManager.isReadableFile(atPath: url.path)
            && fileManager.isWritableFile(atPath: url.path)
    }

    private func requestSystemPermissions() -> Bool {
        // This would typically show a system dialog or guide the user
        // For now, we'll return false to indicate manual intervention needed
        return false
    }

    private func runAutomaticCleanup() -> Bool {
        let storageManager = StorageManager.shared
        storageManager.startAutomaticCleanup(frequencyHours: 1, maxAgeDays: 30)
        return true
    }

    private func resetToDefaultConfiguration() -> Bool {
        let configuration = AugmentConfiguration.shared
        configuration.resetToDefaults()
        return true
    }

    private func restoreDefaultSettings() -> Bool {
        let preferencesManager = PreferencesManager.shared
        preferencesManager.resetToDefaults()
        return true
    }

    private func checkNetworkConnectivity() -> Bool {
        // Simple network check - in a real implementation, this would be more sophisticated
        let url = URL(string: "https://www.apple.com")!
        let semaphore = DispatchSemaphore(value: 0)
        var isConnected = false

        let task = URLSession.shared.dataTask(with: url) { _, _, error in
            isConnected = error == nil
            semaphore.signal()
        }
        task.resume()

        _ = semaphore.wait(timeout: .now() + 5.0)
        return isConnected
    }

    // MARK: - Error Management

    /// Marks an error as recovered and removes it from active errors
    /// - Parameter error: The recovered error
    private func markErrorAsRecovered(_ error: RecoverableError) {
        activeErrors.removeAll { $0.id == error.id }
        recoveryAttempts.removeValue(forKey: error.id)

        // Update error history
        if let index = errorHistory.firstIndex(where: { $0.error.id == error.id }) {
            errorHistory[index].isResolved = true
            errorHistory[index].resolvedAt = Date()
        }

        // Send success notification
        notificationManager.sendErrorRecoverySuccess(errorId: error.id)
    }

    /// Handles recovery failure by trying next strategy or showing dialog
    /// - Parameter error: The error that failed to recover
    private func handleRecoveryFailure(for error: RecoverableError) {
        let attemptCount = recoveryAttempts[error.id] ?? 0

        if attemptCount < maxRecoveryAttempts {
            // Try next automatic strategy
            let usedStrategies = Array(error.recoveryStrategies.prefix(attemptCount))
            if let nextStrategy = error.recoveryStrategies.first(where: { strategy in
                !usedStrategies.contains(strategy) && strategy.isAutomatic
            }) {
                executeRecoveryStrategy(nextStrategy, for: error)
                return
            }
        }

        // No more automatic strategies or max attempts reached
        showErrorDialog(for: error)
    }

    /// Shows error dialog for manual intervention
    /// - Parameter error: The error to show
    private func showErrorDialog(for error: RecoverableError) {
        currentError = error
        isShowingErrorDialog = true
    }

    // MARK: - Error History

    private func recordError(_ error: RecoverableError) {
        let historyEntry = ErrorHistoryEntry(
            error: error,
            occurredAt: Date(),
            isResolved: false,
            resolvedAt: nil
        )

        errorHistory.append(historyEntry)

        // Keep only last 100 errors
        if errorHistory.count > 100 {
            errorHistory.removeFirst(errorHistory.count - 100)
        }

        saveErrorHistory()
    }

    private func loadErrorHistory() {
        // Load from UserDefaults or file
        // Implementation would deserialize saved error history
    }

    private func saveErrorHistory() {
        // Save to UserDefaults or file
        // Implementation would serialize error history
    }

    private func setupErrorMonitoring() {
        // Set up monitoring for system-level errors
        // This could include file system monitoring, memory pressure, etc.
    }

    // MARK: - Public Interface

    /// Gets error history for display in UI
    /// - Returns: Array of error history entries
    public func getErrorHistory() -> [ErrorHistoryEntry] {
        return errorHistory.sorted { $0.occurredAt > $1.occurredAt }
    }

    /// Dismisses the current error dialog
    public func dismissErrorDialog() {
        isShowingErrorDialog = false
        currentError = nil
    }

    /// Manually triggers a recovery strategy
    /// - Parameters:
    ///   - strategy: The strategy to execute
    ///   - error: The error to recover from
    public func executeManualRecovery(_ strategy: RecoveryStrategy, for error: RecoverableError) {
        executeRecoveryStrategy(strategy, for: error)
    }

    /// Clears all resolved errors from active list
    public func clearResolvedErrors() {
        activeErrors.removeAll { error in
            errorHistory.contains { $0.error.id == error.id && $0.isResolved }
        }
    }

    // MARK: - Message Generation

    /// Generates user-friendly message for an error
    /// - Parameters:
    ///   - error: The original error
    ///   - category: The error category
    /// - Returns: User-friendly error message
    private func generateUserMessage(for error: Error, category: ErrorCategory) -> String {
        switch category {
        case .fileSystem:
            return
                "There was a problem accessing a file. This might be due to the file being moved, deleted, or locked by another application."
        case .storage:
            return
                "Your storage space is running low or has reached its limit. Consider freeing up space or adjusting your storage settings."
        case .permissions:
            return
                "Augment doesn't have the necessary permissions to access this file or folder. You may need to grant additional permissions."
        case .network:
            return
                "There was a network connectivity issue. Please check your internet connection and try again."
        case .configuration:
            return
                "There's an issue with your Augment configuration. Resetting to default settings may resolve this problem."
        case .unknown:
            return
                "An unexpected error occurred. Augment will attempt to recover automatically, but you may need to restart the application."
        }
    }

    /// Generates technical details for an error
    /// - Parameter error: The original error
    /// - Returns: Technical error details
    private func generateTechnicalDetails(for error: Error) -> String {
        var details = "Error: \(error.localizedDescription)\n"
        details += "Type: \(type(of: error))\n"

        if let nsError = error as NSError? {
            details += "Domain: \(nsError.domain)\n"
            details += "Code: \(nsError.code)\n"

            if !nsError.userInfo.isEmpty {
                details += "User Info: \(nsError.userInfo)\n"
            }
        }

        details += "Timestamp: \(ISO8601DateFormatter().string(from: Date()))\n"

        return details
    }
}

// MARK: - Data Models

/// Represents an error that can be recovered from
public struct RecoverableError: Identifiable, Equatable {
    public let id: String
    public let originalError: Error
    public let category: ErrorCategory
    public let context: String?
    public let timestamp: Date
    public let recoveryStrategies: [RecoveryStrategy]
    public let userMessage: String
    public let technicalDetails: String

    public static func == (lhs: RecoverableError, rhs: RecoverableError) -> Bool {
        return lhs.id == rhs.id
    }
}

/// Error categories for determining recovery strategies
public enum ErrorCategory: String, CaseIterable {
    case fileSystem = "File System"
    case storage = "Storage"
    case permissions = "Permissions"
    case network = "Network"
    case configuration = "Configuration"
    case unknown = "Unknown"

    var icon: String {
        switch self {
        case .fileSystem:
            return "doc.badge.exclamationmark"
        case .storage:
            return "externaldrive.badge.exclamationmark"
        case .permissions:
            return "lock.badge.exclamationmark"
        case .network:
            return "wifi.exclamationmark"
        case .configuration:
            return "gearshape.badge.exclamationmark"
        case .unknown:
            return "exclamationmark.triangle"
        }
    }
}

/// Available recovery strategies
public enum RecoveryStrategy: String, CaseIterable {
    case retryOperation = "Retry Operation"
    case freeUpSpace = "Free Up Space"
    case checkFilePermissions = "Check File Permissions"
    case requestPermissions = "Request Permissions"
    case runCleanup = "Run Cleanup"
    case resetConfiguration = "Reset Configuration"
    case restoreDefaults = "Restore Defaults"
    case checkNetworkConnection = "Check Network"
    case recreateFile = "Recreate File"
    case adjustStorageSettings = "Adjust Storage Settings"
    case checkSystemSettings = "Check System Settings"
    case runAsAdministrator = "Run as Administrator"
    case workOffline = "Work Offline"
    case reimportSettings = "Reimport Settings"
    case restartApplication = "Restart Application"
    case contactSupport = "Contact Support"

    /// Whether this strategy can be executed automatically
    var isAutomatic: Bool {
        switch self {
        case .retryOperation, .freeUpSpace, .runCleanup, .resetConfiguration, .restoreDefaults,
            .checkNetworkConnection:
            return true
        default:
            return false
        }
    }

    var icon: String {
        switch self {
        case .retryOperation:
            return "arrow.clockwise"
        case .freeUpSpace:
            return "trash"
        case .checkFilePermissions:
            return "checkmark.shield"
        case .requestPermissions:
            return "key"
        case .runCleanup:
            return "sparkles"
        case .resetConfiguration:
            return "gearshape.arrow.triangle.2.circlepath"
        case .restoreDefaults:
            return "arrow.uturn.backward"
        case .checkNetworkConnection:
            return "wifi"
        case .recreateFile:
            return "doc.badge.plus"
        case .adjustStorageSettings:
            return "slider.horizontal.3"
        case .checkSystemSettings:
            return "gearshape"
        case .runAsAdministrator:
            return "person.badge.key"
        case .workOffline:
            return "wifi.slash"
        case .reimportSettings:
            return "square.and.arrow.down"
        case .restartApplication:
            return "power"
        case .contactSupport:
            return "questionmark.circle"
        }
    }
}

/// Error history entry for tracking and analysis
public struct ErrorHistoryEntry {
    public let error: RecoverableError
    public let occurredAt: Date
    public var isResolved: Bool
    public var resolvedAt: Date?
}
