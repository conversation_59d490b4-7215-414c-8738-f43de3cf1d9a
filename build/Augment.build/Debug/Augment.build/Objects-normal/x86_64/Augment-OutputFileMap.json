{"": {"diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/Augment-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/Augment-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/Augment-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/Augment-master.swiftdeps"}, "/Users/<USER>/Desktop/space/AugmentApp/Augment/AugmentApp.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentApp.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentApp.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentApp.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentApp.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentApp.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentApp.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentApp.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentApp~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/Augment/ConflictResolutionView.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolutionView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolutionView.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolutionView.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolutionView.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolutionView.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolutionView.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolutionView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolutionView~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/Augment/ContentView.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ContentView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ContentView.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ContentView.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ContentView.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ContentView.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ContentView.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ContentView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ContentView~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/Augment/SearchView.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchView.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchView.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchView.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchView.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchView.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchView~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/Augment/SpaceDetailView.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SpaceDetailView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SpaceDetailView.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SpaceDetailView.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SpaceDetailView.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SpaceDetailView.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SpaceDetailView.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SpaceDetailView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SpaceDetailView~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/Augment/VersionBrowser.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionBrowser.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionBrowser.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionBrowser.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionBrowser.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionBrowser.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionBrowser.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionBrowser.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionBrowser~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/AugmentSpace.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentSpace.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentSpace.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentSpace.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentSpace.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentSpace.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentSpace.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentSpace.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentSpace~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/BackupManager.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/BackupManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/BackupManager.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/BackupManager.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/BackupManager.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/BackupManager.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/BackupManager.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/BackupManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/BackupManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/ConflictResolution.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolution.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolution.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolution.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolution.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolution.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolution.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolution.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/ConflictResolution~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/FileItem.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileItem.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileItem.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileItem.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileItem.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileItem.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileItem.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileItem.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileItem~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/FileSystemMonitor.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileSystemMonitor.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileSystemMonitor.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileSystemMonitor.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileSystemMonitor.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileSystemMonitor.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileSystemMonitor.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileSystemMonitor.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileSystemMonitor~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/FileType.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileType.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileType.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileType.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileType.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileType.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileType.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileType.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileType~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/NetworkSync.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/NetworkSync.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/NetworkSync.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/NetworkSync.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/NetworkSync.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/NetworkSync.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/NetworkSync.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/NetworkSync.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/NetworkSync~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/PreviewEngine.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/PreviewEngine.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/PreviewEngine.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/PreviewEngine.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/PreviewEngine.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/PreviewEngine.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/PreviewEngine.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/PreviewEngine.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/PreviewEngine~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/SearchEngine.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchEngine.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchEngine.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchEngine.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchEngine.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchEngine.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchEngine.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchEngine.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SearchEngine~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/SnapshotManager.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SnapshotManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SnapshotManager.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SnapshotManager.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SnapshotManager.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SnapshotManager.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SnapshotManager.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SnapshotManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/SnapshotManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentCore/VersionControl.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionControl.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionControl.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionControl.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionControl.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionControl.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionControl.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionControl.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/VersionControl~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentFileSystem/AugmentFUSE.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFUSE.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFUSE.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFUSE.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFUSE.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFUSE.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFUSE.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFUSE.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFUSE~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentFileSystem/AugmentFileSystem.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFileSystem.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFileSystem.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFileSystem.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFileSystem.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFileSystem.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFileSystem.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFileSystem.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/AugmentFileSystem~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/AugmentFileSystem/FileOperationInterceptor.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileOperationInterceptor.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileOperationInterceptor.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileOperationInterceptor.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileOperationInterceptor.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileOperationInterceptor.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileOperationInterceptor.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileOperationInterceptor.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/FileOperationInterceptor~partial.swiftmodule"}, "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/space/AugmentApp/build/Augment.build/Debug/Augment.build/Objects-normal/x86_64/GeneratedAssetSymbols~partial.swiftmodule"}}