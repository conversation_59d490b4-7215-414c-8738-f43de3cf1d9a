{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "704ba1199aef8dfbc6a6d14ce4d99537b8d18fec57e65b775798b6221989d6ad"}], "containerPath": "/Users/<USER>/Documents/space/AugmentApp/Augment.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "macosx", "sdk": "macosx15.5", "sdkVariant": "macos", "supportedArchitectures": ["arm64e", "arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products", "derivedDataPath": "/Users/<USER>/Documents/space/AugmentApp/build", "indexDataStoreFolderPath": "/Users/<USER>/Documents/space/AugmentApp/build/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Documents/space/AugmentApp/build/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "69", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ONLY_ACTIVE_ARCH": "YES"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}