{"": {"diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-master.dia", "emit-module-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-master.swiftdeps"}, "/Users/<USER>/Documents/space/AugmentApp/Augment/AugmentApp.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/Augment/ContentView.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/AugmentSpace.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/BackupManager.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/ConflictResolution.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileItem.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileSystemMonitor.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileType.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/NetworkSync.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/PreviewEngine.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/SearchEngine.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/SnapshotManager.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/VersionControl.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/AugmentFUSE.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/AugmentFileSystem.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/FileOperationInterceptor.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor~partial.swiftmodule"}, "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}}