{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug": {"is-mutated": true}, "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products": {"is-mutated": true}, "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug": {"is-mutated": true}, "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app": {"is-mutated": true}, "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment": {"is-mutated": true}, "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib": {"is-mutated": true}, "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>": {"is-command-timestamp": true}, "<TRIGGER: Validate /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/_CodeSignature", "/Users/<USER>/Documents/space/AugmentApp/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<Linked Binary Debug Dylib /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib>", "<target-Augment-****************************************************************--begin-scanning>", "<target-Augment-****************************************************************--end>", "<target-Augment-****************************************************************--linker-inputs-ready>", "<target-Augment-****************************************************************--modules-ready>", "<workspace-Debug-macosx15.5-macos--stale-file-removal>"], "outputs": ["<all>"]}, "<target-Augment-****************************************************************-Debug-macosx--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/_CodeSignature", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources/Assets.car", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_signature", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Info.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/PkgInfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent.der", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment Swift Compilation Finished", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_lto.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_dependency_info.dat", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment Swift Compilation Requirements Finished", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftmodule", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftsourceinfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.abi.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-Swift.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftdoc", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Augment-Swift.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-target-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-generated-files.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-own-target-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-project-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyMetadataFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-OutputFileMap.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.LinkFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftConstValuesFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json"], "roots": ["/tmp/Augment.dst", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products"], "outputs": ["<target-Augment-****************************************************************-Debug-macosx--arm64-build-headers-stale-file-removal>"]}, "<workspace-Debug-macosx15.5-macos--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment-704ba1199aef8dfbc6a6d14ce4d99537-VFS/all-product-headers.yaml"], "outputs": ["<workspace-Debug-macosx15.5-macos--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/Documents/space/AugmentApp/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/Documents/space/AugmentApp/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<ClangStatCache /Users/<USER>/Documents/space/AugmentApp/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-o", "/Users/<USER>/Documents/space/AugmentApp/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Documents/space/AugmentApp/Augment.xcodeproj", "signature": "c07707eb34a9b8b335cbe9b3ddb7515c"}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/space/AugmentApp/build/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/space/AugmentApp/build/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Products>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment-704ba1199aef8dfbc6a6d14ce4d99537-VFS/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-Augment-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Augment-****************************************************************--begin-compiling>", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyMetadataFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftConstValuesFileList"], "outputs": ["<target-Augment-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--Barrier-ChangePermissions>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-Augment-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--Barrier-StripSymbols>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-Augment-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>", "<CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib>", "<CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib>"], "outputs": ["<target-Augment-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-Augment-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--Barrier-GenerateStubAPI>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-Augment-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-Augment-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--Barrier-CodeSign>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>"], "outputs": ["<target-Augment-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-Augment-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--Barrier-Validate>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--begin-compiling>", "<LSRegisterURL /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>", "<Touch /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>"], "outputs": ["<target-Augment-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-Augment-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--Barrier-CopyAside>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-Augment-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>"], "outputs": ["<target-Augment-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-Augment-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--GeneratedFilesTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ProductStructureTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent.der", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Entitlements.plist"], "outputs": ["<target-Augment-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-target-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-generated-files.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-own-target-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-project-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.hmap"], "outputs": ["<target-Augment-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Info.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/PkgInfo"], "outputs": ["<target-Augment-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--RealityAssetsTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Augment-****************************************************************--ModuleMapTaskProducer>", "<target-Augment-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-Augment-****************************************************************--InfoPlistTaskProducer>", "<target-Augment-****************************************************************--SanitizerTaskProducer>", "<target-Augment-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-Augment-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-Augment-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-Augment-****************************************************************--TestTargetTaskProducer>", "<target-Augment-****************************************************************--TestHostTaskProducer>", "<target-Augment-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-Augment-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-Augment-****************************************************************--DocumentationTaskProducer>", "<target-Augment-****************************************************************--CustomTaskProducer>", "<target-Augment-****************************************************************--StubBinaryTaskProducer>", "<target-Augment-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--start>", "<target-Augment-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents>", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS>", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources>"], "outputs": ["<target-Augment-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--HeadermapTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Augment-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>"], "outputs": ["<target-Augment-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-Augment-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-Augment-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-Augment-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources/Assets.car", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment Swift Compilation Finished", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftmodule", "<Linked Binary /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_lto.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment Swift Compilation Requirements Finished", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftmodule", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftsourceinfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.abi.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-Swift.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftdoc", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-OutputFileMap.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.LinkFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json"], "outputs": ["<target-Augment-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-Augment-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-Augment-****************************************************************--generated-headers>"]}, "P0:::Gate target-Augment-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment Swift Compilation Requirements Finished", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftmodule", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftsourceinfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.abi.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-Swift.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftdoc", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Augment-Swift.h"], "outputs": ["<target-Augment-****************************************************************--swift-generated-headers>"]}, "P0:target-Augment-****************************************************************-:Debug:CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets/", "/Users/<USER>/Documents/space/AugmentApp/Augment/AugmentApp.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/ConflictResolutionView.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/ContentView.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/space/AugmentApp/Augment/SearchView.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/SpaceDetailView.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/VersionBrowser.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/AugmentSpace.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/BackupManager.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/ConflictResolution.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileItem.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileSystemMonitor.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileType.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/NetworkSync.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/PreviewEngine.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/SearchEngine.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/SnapshotManager.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/VersionControl.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/AugmentFUSE.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/AugmentFileSystem.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/FileOperationInterceptor.swift/", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent/", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Info.plist/", "<CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib>", "<CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib>", "<target-Augment-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment normal>", "<TRIGGER: MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/_CodeSignature", "<CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>", "<TRIGGER: CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>"]}, "P0:target-Augment-****************************************************************-:Debug:CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets/", "/Users/<USER>/Documents/space/AugmentApp/Augment/AugmentApp.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/ConflictResolutionView.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/ContentView.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/space/AugmentApp/Augment/SearchView.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/SpaceDetailView.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/VersionBrowser.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/AugmentSpace.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/BackupManager.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/ConflictResolution.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileItem.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileSystemMonitor.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileType.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/NetworkSync.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/PreviewEngine.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/SearchEngine.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/SnapshotManager.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/VersionControl.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/AugmentFUSE.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/AugmentFileSystem.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/FileOperationInterceptor.swift/", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Info.plist/", "<target-Augment-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib>"]}, "P0:target-Augment-****************************************************************-:Debug:CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets/", "/Users/<USER>/Documents/space/AugmentApp/Augment/AugmentApp.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/ConflictResolutionView.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/ContentView.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/space/AugmentApp/Augment/SearchView.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/SpaceDetailView.swift/", "/Users/<USER>/Documents/space/AugmentApp/Augment/VersionBrowser.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/AugmentSpace.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/BackupManager.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/ConflictResolution.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileItem.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileSystemMonitor.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileType.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/NetworkSync.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/PreviewEngine.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/SearchEngine.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/SnapshotManager.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/VersionControl.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/AugmentFUSE.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/AugmentFileSystem.swift/", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/FileOperationInterceptor.swift/", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Info.plist/", "<CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib>", "<target-Augment-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib>"]}, "P0:target-Augment-****************************************************************-:Debug:CompileAssetCatalogVariant thinned /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources /Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources /Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets", "--compile", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "13.0", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/Documents/space/AugmentApp", "control-enabled": false, "deps": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "91e59a9d3ca81c2d8793c7e2628a0546"}, "P0:target-Augment-****************************************************************-:Debug:CompileAssetCatalogVariant unthinned /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources /Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources /Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets", "--compile", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "13.0", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/Documents/space/AugmentApp", "control-enabled": false, "deps": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "e3708b204135617742edf96c1a88070d"}, "P0:target-Augment-****************************************************************-:Debug:CopySwiftLibs /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Augment-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>"], "deps": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-Augment-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/Augment/ContentView.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/AugmentApp.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/SpaceDetailView.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/SearchView.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/ConflictResolutionView.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/VersionBrowser.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/BackupManager.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/PreviewEngine.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/FileOperationInterceptor.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/AugmentFileSystem.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/VersionControl.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileType.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/AugmentSpace.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileItem.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/SearchEngine.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/AugmentFUSE.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileSystemMonitor.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/ConflictResolution.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/NetworkSync.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/SnapshotManager.swift", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyMetadataFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_dependency_info.dat", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftConstValuesFileList", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Augment-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "Augment", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "--xcode-version", "16F6", "--platform-family", "macOS", "--deployment-target", "13.0", "--bundle-identifier", "com.augment.Augment", "--output", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources", "--target-triple", "arm64-apple-macos13.0", "--binary-file", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment", "--dependency-file", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftFileList", "--metadata-file-list", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/Documents/space/AugmentApp", "signature": "4dbcb6d8261c91a67cb1b2fae055dbd6"}, "P0:target-Augment-****************************************************************-:Debug:Gate target-Augment-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************-Debug-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Augment.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>"], "outputs": ["<target-Augment-****************************************************************--begin-compiling>"]}, "P0:target-Augment-****************************************************************-:Debug:Gate target-Augment-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************-Debug-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Augment.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>"], "outputs": ["<target-Augment-****************************************************************--begin-linking>"]}, "P0:target-Augment-****************************************************************-:Debug:Gate target-Augment-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************-Debug-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Augment.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--begin-scanning>"]}, "P0:target-Augment-****************************************************************-:Debug:Gate target-Augment-****************************************************************--end": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--entry>", "<CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>", "<CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib>", "<CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources/Assets.car", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned>", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents>", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS>", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Info.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/PkgInfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>", "<LSRegisterURL /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment Swift Compilation Finished", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>", "<Validate /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>", "<ValidateDevelopmentAssets-/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftmodule", "<Linked Binary /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_lto.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment Swift Compilation Requirements Finished", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftmodule", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftsourceinfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.abi.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-Swift.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftdoc", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Augment-Swift.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Augment-Swift.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-target-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-generated-files.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-own-target-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-project-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyMetadataFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-OutputFileMap.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.LinkFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftConstValuesFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json", "<target-Augment-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-Augment-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Augment-****************************************************************--Barrier-ChangePermissions>", "<target-Augment-****************************************************************--Barrier-CodeSign>", "<target-Augment-****************************************************************--Barrier-CopyAside>", "<target-Augment-****************************************************************--Barrier-GenerateStubAPI>", "<target-Augment-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Augment-****************************************************************--Barrier-RegisterProduct>", "<target-Augment-****************************************************************--Barrier-StripSymbols>", "<target-Augment-****************************************************************--Barrier-Validate>", "<target-Augment-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-Augment-****************************************************************--CustomTaskProducer>", "<target-Augment-****************************************************************--DocumentationTaskProducer>", "<target-Augment-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Augment-****************************************************************--GeneratedFilesTaskProducer>", "<target-Augment-****************************************************************--HeadermapTaskProducer>", "<target-Augment-****************************************************************--InfoPlistTaskProducer>", "<target-Augment-****************************************************************--ModuleMapTaskProducer>", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Augment-****************************************************************--ProductStructureTaskProducer>", "<target-Augment-****************************************************************--RealityAssetsTaskProducer>", "<target-Augment-****************************************************************--SanitizerTaskProducer>", "<target-Augment-****************************************************************--StubBinaryTaskProducer>", "<target-Augment-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-Augment-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-Augment-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-Augment-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-Augment-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-Augment-****************************************************************--TestHostTaskProducer>", "<target-Augment-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-Augment-****************************************************************--TestTargetTaskProducer>", "<target-Augment-****************************************************************--copy-headers-completion>", "<target-Augment-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Augment-****************************************************************--generated-headers>", "<target-Augment-****************************************************************--swift-generated-headers>"], "outputs": ["<target-Augment-****************************************************************--end>"]}, "P0:target-Augment-****************************************************************-:Debug:Gate target-Augment-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************-Debug-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Augment.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>", "<target-Augment-****************************************************************--begin-compiling>"], "outputs": ["<target-Augment-****************************************************************--entry>"]}, "P0:target-Augment-****************************************************************-:Debug:Gate target-Augment-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************-Debug-macosx--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Augment.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug>", "<CreateBuildDirectory-/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug>"], "outputs": ["<target-Augment-****************************************************************--immediate>"]}, "P0:target-Augment-****************************************************************-:Debug:Gate target-Augment-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-ExecutorLinkFileList-normal-arm64.txt", "<Linked Binary /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_lto.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment Swift Compilation Requirements Finished", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftmodule", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftsourceinfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.abi.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-Swift.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftdoc", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.LinkFileList"], "outputs": ["<target-Augment-****************************************************************--linker-inputs-ready>"]}, "P0:target-Augment-****************************************************************-:Debug:Gate target-Augment-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment Swift Compilation Requirements Finished", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftmodule", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftsourceinfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.abi.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-Swift.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftdoc", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Augment-Swift.h"], "outputs": ["<target-Augment-****************************************************************--modules-ready>"]}, "P0:target-Augment-****************************************************************-:Debug:Gate target-Augment-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources/Assets.car", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent.der", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment Swift Compilation Finished", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftmodule", "<Linked Binary /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_lto.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment Swift Compilation Requirements Finished", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftmodule", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftsourceinfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.abi.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-Swift.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftdoc", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Augment-Swift.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyMetadataFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-OutputFileMap.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.LinkFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftConstValuesFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json", "<target-Augment-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-Augment-****************************************************************--unsigned-product-ready>"]}, "P0:target-Augment-****************************************************************-:Debug:Gate target-Augment-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-Augment-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-Augment-****************************************************************--will-sign>"]}, "P0:target-Augment-****************************************************************-:Debug:GenerateAssetSymbols /Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets/", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets", "--compile", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "13.0", "--platform", "macosx", "--bundle-identifier", "com.augment.Augment", "--generate-swift-asset-symbols", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Documents/space/AugmentApp", "control-enabled": false, "signature": "7a38ffd2cd44827a665df196c4d6afb0"}, "P0:target-Augment-****************************************************************-:Debug:LinkAssetCatalog /Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/space/AugmentApp/Augment/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_signature", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources/Assets.car"], "deps": "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_dependencies"}, "P0:target-Augment-****************************************************************-:Debug:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-Augment-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/thinned>"]}, "P0:target-Augment-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_output/unthinned>"]}, "P0:target-Augment-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app", "inputs": ["<target-Augment-****************************************************************--start>", "<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>", "<TRIGGER: MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>"]}, "P0:target-Augment-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents", "inputs": ["<target-Augment-****************************************************************--start>", "<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents>"]}, "P0:target-Augment-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS", "inputs": ["<target-Augment-****************************************************************--start>", "<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS>"]}, "P0:target-Augment-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources", "inputs": ["<target-Augment-****************************************************************--start>", "<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources", "<MkDir /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Resources>"]}, "P0:target-Augment-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Info.plist /Users/<USER>/Documents/space/AugmentApp/Augment/Info.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Info.plist /Users/<USER>/Documents/space/AugmentApp/Augment/Info.plist", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/Augment/Info.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/assetcatalog_generated_info.plist", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Info.plist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/PkgInfo"]}, "P0:target-Augment-****************************************************************-:Debug:ProcessProductPackaging /Users/<USER>/Documents/space/AugmentApp/Augment/Augment.entitlements /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging /Users/<USER>/Documents/space/AugmentApp/Augment/Augment.entitlements /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/Augment/Augment.entitlements", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Entitlements.plist", "<target-Augment-****************************************************************--ProductStructureTaskProducer>", "<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent"]}, "P0:target-Augment-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent.der", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent", "<target-Augment-****************************************************************--ProductStructureTaskProducer>", "<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent", "-o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.app.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Documents/space/AugmentApp", "signature": "094206f4b9a9c1fccf8500c98e140c8d"}, "P0:target-Augment-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app", "<target-Augment-****************************************************************--Barrier-CodeSign>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>"]}, "P0:target-Augment-****************************************************************-:Debug:RegisterWithLaunchServices /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app": {"tool": "lsregisterurl", "description": "RegisterWithLaunchServices /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app", "inputs": ["<target-Augment-****************************************************************--Barrier-Validate>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--entry>", "<TRIGGER: Validate /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>"], "outputs": ["<LSRegisterURL /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>"]}, "P0:target-Augment-****************************************************************-:Debug:SwiftDriver Compilation Augment normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation Augment normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/Augment/ContentView.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/AugmentApp.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/SpaceDetailView.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/SearchView.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/ConflictResolutionView.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/VersionBrowser.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/BackupManager.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/PreviewEngine.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/FileOperationInterceptor.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/AugmentFileSystem.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/VersionControl.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileType.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/AugmentSpace.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileItem.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/SearchEngine.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/AugmentFUSE.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileSystemMonitor.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/ConflictResolution.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/NetworkSync.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/SnapshotManager.swift", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-OutputFileMap.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-generated-files.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-own-target-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-target-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-project-headers.hmap", "<ClangStatCache /Users/<USER>/Documents/space/AugmentApp/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-Augment-****************************************************************--generated-headers>", "<target-Augment-****************************************************************--copy-headers-completion>", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment Swift Compilation Finished", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-Augment-****************************************************************-:Debug:Touch /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app": {"tool": "shell", "description": "Touch /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app", "<target-Augment-****************************************************************--Barrier-Validate>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app"], "env": {}, "working-directory": "/Users/<USER>/Documents/space/AugmentApp", "signature": "6eb9a52bf1ff0e66f0850bb9ff8dca41"}, "P0:target-Augment-****************************************************************-:Debug:Validate /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/Info.plist", "<target-Augment-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Augment-****************************************************************--will-sign>", "<target-Augment-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>"], "outputs": ["<Validate /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>", "<TRIGGER: Validate /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app>"]}, "P0:target-Augment-****************************************************************-:Debug:ValidateDevelopmentAssets /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/Augment/Preview Content", "<target-Augment-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment-704ba1199aef8dfbc6a6d14ce4d99537-VFS/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment-704ba1199aef8dfbc6a6d14ce4d99537-VFS/all-product-headers.yaml", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment-704ba1199aef8dfbc6a6d14ce4d99537-VFS/all-product-headers.yaml"]}, "P2:target-Augment-****************************************************************-:Debug:ConstructStubExecutorLinkFileList /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-ExecutorLinkFileList-normal-arm64.txt": {"tool": "construct-stub-executor-input-file-list", "description": "ConstructStubExecutorLinkFileList /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-ExecutorLinkFileList-normal-arm64.txt", "inputs": ["/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/libPreviewsJITStubExecutor_no_swift_entry_point.a", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/libPreviewsJITStubExecutor.a", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-ExecutorLinkFileList-normal-arm64.txt"]}, "P2:target-Augment-****************************************************************-:Debug:Copy /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftsourceinfo", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftsourceinfo/", "<target-Augment-****************************************************************--copy-headers-completion>", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo"]}, "P2:target-Augment-****************************************************************-:Debug:Copy /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.abi.json", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.abi.json/", "<target-Augment-****************************************************************--copy-headers-completion>", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.abi.json"]}, "P2:target-Augment-****************************************************************-:Debug:Copy /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftdoc", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftdoc/", "<target-Augment-****************************************************************--copy-headers-completion>", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftdoc"]}, "P2:target-Augment-****************************************************************-:Debug:Copy /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftmodule", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftmodule/", "<target-Augment-****************************************************************--copy-headers-completion>", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.swiftmodule/arm64-apple-macos.swiftmodule"]}, "P2:target-Augment-****************************************************************-:Debug:Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment normal": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment normal", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-ExecutorLinkFileList-normal-arm64.txt", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment", "<Linked Binary /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment>", "<TRIGGER: Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-macos13.0", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-O0", "-L/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug", "-F/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-e", "___debug_blank_executor_main", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_dylib", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibPath-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_instlnm", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibInstallName-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-filelist", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment"], "env": {}, "working-directory": "/Users/<USER>/Documents/space/AugmentApp", "signature": "1630e60cb7519a548071ea20cdb82214"}, "P2:target-Augment-****************************************************************-:Debug:Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib normal", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentApp.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SpaceDetailView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolutionView.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionBrowser.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/BackupManager.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/PreviewEngine.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileOperationInterceptor.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFileSystem.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/VersionControl.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileType.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentSpace.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileItem.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SearchEngine.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/AugmentFUSE.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/FileSystemMonitor.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/ConflictResolution.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/NetworkSync.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/SnapshotManager.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.LinkFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug", "<target-Augment-****************************************************************--generated-headers>", "<target-Augment-****************************************************************--swift-generated-headers>", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib", "<Linked Binary Debug Dylib /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_lto.o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-macos13.0", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-O0", "-L/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug", "-L/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug", "-F/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug", "-F/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug", "-filelist", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.LinkFileList", "-install_name", "@rpath/Augment.debug.dylib", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftmodule", "-<PERSON><PERSON><PERSON>", "-alias", "-<PERSON><PERSON><PERSON>", "_main", "-<PERSON><PERSON><PERSON>", "___debug_main_executable_dylib_entry_point", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/Augment.debug.dylib"], "env": {}, "working-directory": "/Users/<USER>/Documents/space/AugmentApp", "deps": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_dependency_info.dat"], "deps-style": "dependency-info", "signature": "5ab8d0e51eeba4879fbe81033f6f1976"}, "P2:target-Augment-****************************************************************-:Debug:Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib normal", "inputs": ["<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib>", "<TRIGGER: Ld /Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-macos13.0", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-O0", "-L/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug", "-F/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug", "-install_name", "@rpath/Augment.debug.dylib", "-dead_strip", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_dependency_info.dat", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Products/Debug/Augment.app/Contents/MacOS/__preview.dylib"], "env": {}, "working-directory": "/Users/<USER>/Documents/space/AugmentApp", "signature": "fb9fe08d1e37573088a0d60ff1056905"}, "P2:target-Augment-****************************************************************-:Debug:SwiftDriver Compilation Requirements Augment normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements Augment normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/Augment/ContentView.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/AugmentApp.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/SpaceDetailView.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/SearchView.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/ConflictResolutionView.swift", "/Users/<USER>/Documents/space/AugmentApp/Augment/VersionBrowser.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/BackupManager.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/PreviewEngine.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/FileOperationInterceptor.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/AugmentFileSystem.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/VersionControl.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileType.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/AugmentSpace.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileItem.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/SearchEngine.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentFileSystem/AugmentFUSE.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/FileSystemMonitor.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/ConflictResolution.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/NetworkSync.swift", "/Users/<USER>/Documents/space/AugmentApp/AugmentCore/SnapshotManager.swift", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftFileList", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-OutputFileMap.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-generated-files.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-own-target-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-target-headers.hmap", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-project-headers.hmap", "<ClangStatCache /Users/<USER>/Documents/space/AugmentApp/build/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-Augment-****************************************************************--copy-headers-completion>", "<target-Augment-****************************************************************--ModuleVerifierTaskProducer>", "<target-Augment-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment Swift Compilation Requirements Finished", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftmodule", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftsourceinfo", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.abi.json", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-Swift.h", "/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.swiftdoc"]}, "P2:target-Augment-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Augment-Swift.h /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Augment-Swift.h /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-Swift.h", "inputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-Swift.h", "<target-Augment-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Augment-Swift.h"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibInstallName-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibInstallName-normal-arm64.txt", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibInstallName-normal-arm64.txt"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibPath-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibPath-normal-arm64.txt", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-DebugDylibPath-normal-arm64.txt"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-non-framework-target-headers.hmap", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-non-framework-target-headers.hmap"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-target-headers.hmap", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-all-target-headers.hmap"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-generated-files.hmap", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-generated-files.hmap"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-own-target-headers.hmap", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-own-target-headers.hmap"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-project-headers.hmap", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment-project-headers.hmap"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyMetadataFileList", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyMetadataFileList"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyStaticMetadataFileList", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.DependencyStaticMetadataFileList"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.hmap", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Augment.hmap"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Entitlements.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Entitlements.plist", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/DerivedSources/Entitlements.plist"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-OutputFileMap.json", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment-OutputFileMap.json"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.LinkFileList", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.LinkFileList"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftConstValuesFileList", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftConstValuesFileList"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftFileList", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment.SwiftFileList"]}, "P2:target-Augment-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json", "inputs": ["<target-Augment-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/space/AugmentApp/build/Build/Intermediates.noindex/Augment.build/Debug/Augment.build/Objects-normal/arm64/Augment_const_extract_protocols.json"]}}}