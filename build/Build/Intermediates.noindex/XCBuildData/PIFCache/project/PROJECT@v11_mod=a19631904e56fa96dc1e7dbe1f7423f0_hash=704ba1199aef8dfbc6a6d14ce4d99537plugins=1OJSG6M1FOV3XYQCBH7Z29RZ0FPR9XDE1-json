{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEAD_CODE_STRIPPING": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "2TKF278BYV", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MACOSX_DEPLOYMENT_TARGET": "13.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "macosx", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "704ba1199aef8dfbc6a6d14ce4d995375bc158914d3f0a7fee4d27588908fdfb", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEAD_CODE_STRIPPING": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "2TKF278BYV", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MACOSX_DEPLOYMENT_TARGET": "13.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "macosx", "SWIFT_COMPILATION_MODE": "wholemodule"}, "guid": "704ba1199aef8dfbc6a6d14ce4d99537f9b2144fb260326251084627a66d5ce7", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d9953750b809d7acf0ad2f504af59fbbdcdb0c", "path": "FileType.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d9953735302ae47db6aae21e2129893e792537", "path": "AugmentSpace.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d99537c36158b03d1c1c02c0f5ff864bebacfc", "path": "FileItem.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d99537d7ff319b68a98bcb034141ccfb8551c9", "path": "BackupManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d995374884c282d65793cba1642c4b6c031cba", "path": "ConflictResolution.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d995375ab5076a819e3f823b8e0db08674870f", "path": "FileSystemMonitor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d995375250bdbcf70ab305ff4fee93c3b64bc4", "path": "NetworkSync.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d99537475101d31a9af60a33fc710d2324a5aa", "path": "PreviewEngine.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d995375a3bda6c43b397d6bb663d407c6b2638", "path": "SearchEngine.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d995371ef677ca6a2336d0609fd05fffbfd801", "path": "SnapshotManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d99537caef8c2b5c59cc64a4a816007ea83f71", "path": "VersionControl.swift", "sourceTree": "<group>", "type": "file"}], "guid": "704ba1199aef8dfbc6a6d14ce4d9953753267be2575a96974a08418b011841d4", "name": "AugmentCore", "path": "AugmentCore", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d995375a2ea9466fead79ccf020f17946cebe3", "path": "AugmentFileSystem.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d99537ad27a96ff24eed0c1feaa5c12ef030a4", "path": "AugmentFUSE.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d995372841f1384d4ad4a2a26410a86b96e025", "path": "FileOperationInterceptor.swift", "sourceTree": "<group>", "type": "file"}], "guid": "704ba1199aef8dfbc6a6d14ce4d99537891de26288cead910ef12c5029ea7148", "name": "AugmentFileSystem", "path": "AugmentFileSystem", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d9953741c1cedfd05a9bfb8e02143add16960f", "path": "AugmentApp.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "704ba1199aef8dfbc6a6d14ce4d99537a45cf0e6bbbc8c9c11a0ca388821a1eb", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "704ba1199aef8dfbc6a6d14ce4d9953715cc1728d34eb23aa32d5db2ee546cdd", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "704ba1199aef8dfbc6a6d14ce4d99537517ffccc44fbc84167e32a5be5e21d66", "path": "Augment.entitlements", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "704ba1199aef8dfbc6a6d14ce4d99537107cb2bf24f614d7c43524b48ef528c3", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "704ba1199aef8dfbc6a6d14ce4d9953711ebed420ec07a0e171334f37e25e8cc", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "704ba1199aef8dfbc6a6d14ce4d99537d5bdac5ead61e7de1d3ef5b2e74ba4bc", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}], "guid": "704ba1199aef8dfbc6a6d14ce4d99537458c039c3f28928f5b4bceb7fa8b1e5a", "name": "Augment", "path": "Augment", "sourceTree": "<group>", "type": "group"}, {"guid": "704ba1199aef8dfbc6a6d14ce4d9953712c0eaef53222f3a35bb41d18f696293", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "704ba1199aef8dfbc6a6d14ce4d99537c348c05e8f0bc7525bc7b2680e2bd101", "name": "Augment", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "704ba1199aef8dfbc6a6d14ce4d99537", "path": "/Users/<USER>/Documents/space/AugmentApp/Augment.xcodeproj", "projectDirectory": "/Users/<USER>/Documents/space/AugmentApp", "targets": ["TARGET@v11_hash=a75bf6a8b1e29924c9460c2bbabc5398"]}