{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_ENTITLEMENTS": "Augment/Augment.entitlements", "CODE_SIGN_STYLE": "Automatic", "COMBINE_HIDPI_IMAGES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEAD_CODE_STRIPPING": "YES", "DEVELOPMENT_ASSET_PATHS": "\"Augment/Preview Content\"", "ENABLE_PREVIEWS": "YES", "INFOPLIST_FILE": "Augment/Info.plist", "INFOPLIST_KEY_NSHumanReadableCopyright": "", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/../Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.augment.Augment", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0"}, "guid": "704ba1199aef8dfbc6a6d14ce4d995379a9eb647f5c254fb6bf1da4274d92844", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_ENTITLEMENTS": "Augment/Augment.entitlements", "CODE_SIGN_STYLE": "Automatic", "COMBINE_HIDPI_IMAGES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEAD_CODE_STRIPPING": "YES", "DEVELOPMENT_ASSET_PATHS": "\"Augment/Preview Content\"", "ENABLE_PREVIEWS": "YES", "INFOPLIST_FILE": "Augment/Info.plist", "INFOPLIST_KEY_NSHumanReadableCopyright": "", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/../Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.augment.Augment", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0"}, "guid": "704ba1199aef8dfbc6a6d14ce4d9953746f7d5d864d23630bd3c57f41eb605fb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "704ba1199aef8dfbc6a6d14ce4d99537a45cf0e6bbbc8c9c11a0ca388821a1eb", "guid": "704ba1199aef8dfbc6a6d14ce4d9953774da171ebca5570981319bed092c61f1"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d9953741c1cedfd05a9bfb8e02143add16960f", "guid": "704ba1199aef8dfbc6a6d14ce4d99537249400898fb3b26204d1d3fd0d791fda"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d99537a93bff1519a0f56216ccd198883efa4e", "guid": "704ba1199aef8dfbc6a6d14ce4d99537d0a068e8195a3993c19eab1cb5e13484"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d99537ceec7e33f132fdf40fbee2e299276b97", "guid": "704ba1199aef8dfbc6a6d14ce4d995379daf02d67a92210fe7c3c815f2ebac3c"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d99537d9d40dba68676fb8867d692f327403f0", "guid": "704ba1199aef8dfbc6a6d14ce4d99537a6a0dccbd2d623b8e34672044470f2df"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d9953733be9f74b9fa68c476464b6974b603a6", "guid": "704ba1199aef8dfbc6a6d14ce4d995370f4d926dd3164b6d3779214d30e4f551"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d99537d7ff319b68a98bcb034141ccfb8551c9", "guid": "704ba1199aef8dfbc6a6d14ce4d9953722aa278fe45ba43e835516ae1686b46d"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d99537475101d31a9af60a33fc710d2324a5aa", "guid": "704ba1199aef8dfbc6a6d14ce4d99537ad67fb5874c9fe26c434b9db68530d3a"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d995372841f1384d4ad4a2a26410a86b96e025", "guid": "704ba1199aef8dfbc6a6d14ce4d99537cf6f6f90d615c4be81ec3dbe5c507b22"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d995375a2ea9466fead79ccf020f17946cebe3", "guid": "704ba1199aef8dfbc6a6d14ce4d99537b15375fed07d0275e5136bbf3981cf33"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d99537caef8c2b5c59cc64a4a816007ea83f71", "guid": "704ba1199aef8dfbc6a6d14ce4d995374da4654484fd1eaba19fa80207605793"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d9953750b809d7acf0ad2f504af59fbbdcdb0c", "guid": "704ba1199aef8dfbc6a6d14ce4d99537f087775ca09a020afe9b08bc65726d33"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d9953735302ae47db6aae21e2129893e792537", "guid": "704ba1199aef8dfbc6a6d14ce4d9953703770eb521c918bc64b3b405d73647f6"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d99537c36158b03d1c1c02c0f5ff864bebacfc", "guid": "704ba1199aef8dfbc6a6d14ce4d995376d53821749c264b1cbc9f0271bd2e206"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d995375a3bda6c43b397d6bb663d407c6b2638", "guid": "704ba1199aef8dfbc6a6d14ce4d9953732becacd04fcef3ca2b8e49d4c73089a"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d99537ad27a96ff24eed0c1feaa5c12ef030a4", "guid": "704ba1199aef8dfbc6a6d14ce4d995375ed4436937d9dba74a13a84fbda26bb8"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d995375ab5076a819e3f823b8e0db08674870f", "guid": "704ba1199aef8dfbc6a6d14ce4d9953757e43f4369ec65b5829ca8f0dc426668"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d995374884c282d65793cba1642c4b6c031cba", "guid": "704ba1199aef8dfbc6a6d14ce4d995376f599ff4a6414a0460a3d8b32fd07ee3"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d995375250bdbcf70ab305ff4fee93c3b64bc4", "guid": "704ba1199aef8dfbc6a6d14ce4d9953726b4cd53d674867c97afba00c4521b67"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d995371ef677ca6a2336d0609fd05fffbfd801", "guid": "704ba1199aef8dfbc6a6d14ce4d995373000b96cd1fc9d769b66a26411f46f01"}], "guid": "704ba1199aef8dfbc6a6d14ce4d99537d2e5802bc934de901dc7c2ab557f095f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "704ba1199aef8dfbc6a6d14ce4d9953755ff411eb255ef037a47a4229df988b5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "704ba1199aef8dfbc6a6d14ce4d9953711ebed420ec07a0e171334f37e25e8cc", "guid": "704ba1199aef8dfbc6a6d14ce4d995379997c15eb27c0e7530908c7a80f1eb56"}, {"fileReference": "704ba1199aef8dfbc6a6d14ce4d9953715cc1728d34eb23aa32d5db2ee546cdd", "guid": "704ba1199aef8dfbc6a6d14ce4d99537e4eace69c2586c335e56102a052ea347"}], "guid": "704ba1199aef8dfbc6a6d14ce4d99537fee9434db87e35a7b1cefa8c9fafdd54", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "704ba1199aef8dfbc6a6d14ce4d99537b8d18fec57e65b775798b6221989d6ad", "name": "Augment", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "704ba1199aef8dfbc6a6d14ce4d9953769d748f5f9af345dbbf4d31a6871e455", "name": "Augment.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}