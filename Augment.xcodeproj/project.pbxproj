// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		017C36E82DFB077300D661A9 /* BackupManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36D92DFB077300D661A9 /* BackupManager.swift */; };
		017C36E92DFB077300D661A9 /* PreviewEngine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36DD2DFB077300D661A9 /* PreviewEngine.swift */; };
		017C36EA2DFB077300D661A9 /* FileOperationInterceptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36E52DFB077300D661A9 /* FileOperationInterceptor.swift */; };
		017C36EB2DFB077300D661A9 /* AugmentFileSystem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36E32DFB077300D661A9 /* AugmentFileSystem.swift */; };
		017C36EC2DFB077300D661A9 /* VersionControl.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36E12DFB077300D661A9 /* VersionControl.swift */; };
		017C36ED2DFB077300D661A9 /* SearchEngine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36DF2DFB077300D661A9 /* SearchEngine.swift */; };
		017C36EE2DFB077300D661A9 /* AugmentFUSE.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36E42DFB077300D661A9 /* AugmentFUSE.swift */; };
		017C36EF2DFB077300D661A9 /* FileSystemMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36DB2DFB077300D661A9 /* FileSystemMonitor.swift */; };
		017C36F02DFB077300D661A9 /* ConflictResolution.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36DA2DFB077300D661A9 /* ConflictResolution.swift */; };
		017C36F12DFB077300D661A9 /* NetworkSync.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36DC2DFB077300D661A9 /* NetworkSync.swift */; };
		017C36F22DFB077300D661A9 /* SnapshotManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36E02DFB077300D661A9 /* SnapshotManager.swift */; };
		017C36F62DFB0ECB00D661A9 /* FileType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36F52DFB0ECB00D661A9 /* FileType.swift */; };
		017C36F72DFB0ECB00D661A9 /* AugmentSpace.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36F82DFB0ECB00D661A9 /* AugmentSpace.swift */; };
		017C36F92DFB0ECB00D661A9 /* FileItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36FA2DFB0ECB00D661A9 /* FileItem.swift */; };
		8A1234561234567800000001 /* AugmentApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000002 /* AugmentApp.swift */; };
		8A1234561234567800000003 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000004 /* ContentView.swift */; };
		8A1234561234567800000005 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000006 /* Assets.xcassets */; };
		8A1234561234567800000007 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000008 /* Preview Assets.xcassets */; };
		8A123456123456780000002E /* SpaceDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A123456123456780000002A /* SpaceDetailView.swift */; };
		8A123456123456780000002F /* SearchView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A123456123456780000002B /* SearchView.swift */; };
		8A1234561234567800000030 /* ConflictResolutionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A123456123456780000002C /* ConflictResolutionView.swift */; };
		8A1234561234567800000031 /* VersionBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A123456123456780000002D /* VersionBrowser.swift */; };

/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		017C36D92DFB077300D661A9 /* BackupManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BackupManager.swift; sourceTree = "<group>"; };
		017C36DA2DFB077300D661A9 /* ConflictResolution.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConflictResolution.swift; sourceTree = "<group>"; };
		017C36DB2DFB077300D661A9 /* FileSystemMonitor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileSystemMonitor.swift; sourceTree = "<group>"; };
		017C36DC2DFB077300D661A9 /* NetworkSync.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkSync.swift; sourceTree = "<group>"; };
		017C36DD2DFB077300D661A9 /* PreviewEngine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreviewEngine.swift; sourceTree = "<group>"; };
		017C36DF2DFB077300D661A9 /* SearchEngine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchEngine.swift; sourceTree = "<group>"; };
		017C36E02DFB077300D661A9 /* SnapshotManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SnapshotManager.swift; sourceTree = "<group>"; };
		017C36E12DFB077300D661A9 /* VersionControl.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VersionControl.swift; sourceTree = "<group>"; };
		017C36E32DFB077300D661A9 /* AugmentFileSystem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AugmentFileSystem.swift; sourceTree = "<group>"; };
		017C36E42DFB077300D661A9 /* AugmentFUSE.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AugmentFUSE.swift; sourceTree = "<group>"; };
		017C36E52DFB077300D661A9 /* FileOperationInterceptor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileOperationInterceptor.swift; sourceTree = "<group>"; };
		017C36F52DFB0ECB00D661A9 /* FileType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileType.swift; sourceTree = "<group>"; };
	017C36F82DFB0ECB00D661A9 /* AugmentSpace.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AugmentSpace.swift; sourceTree = "<group>"; };
	017C36FA2DFB0ECB00D661A9 /* FileItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileItem.swift; sourceTree = "<group>"; };
		8A1234561234567800000002 /* AugmentApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AugmentApp.swift; sourceTree = "<group>"; };
		8A1234561234567800000004 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		8A1234561234567800000006 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		8A1234561234567800000008 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		8A1234561234567800000009 /* Augment.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Augment.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8A123456123456780000000A /* Augment.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Augment.entitlements; sourceTree = "<group>"; };
		8A123456123456780000001F /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8A123456123456780000002A /* SpaceDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SpaceDetailView.swift; sourceTree = "<group>"; };
		8A123456123456780000002B /* SearchView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchView.swift; sourceTree = "<group>"; };
		8A123456123456780000002C /* ConflictResolutionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConflictResolutionView.swift; sourceTree = "<group>"; };
		8A123456123456780000002D /* VersionBrowser.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VersionBrowser.swift; sourceTree = "<group>"; };

/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8A123456123456780000000B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		017C36E22DFB077300D661A9 /* AugmentCore */ = {
			isa = PBXGroup;
			children = (
				017C36F52DFB0ECB00D661A9 /* FileType.swift */,
				017C36F82DFB0ECB00D661A9 /* AugmentSpace.swift */,
				017C36FA2DFB0ECB00D661A9 /* FileItem.swift */,
				017C36D92DFB077300D661A9 /* BackupManager.swift */,
				017C36DA2DFB077300D661A9 /* ConflictResolution.swift */,
				017C36DB2DFB077300D661A9 /* FileSystemMonitor.swift */,
				017C36DC2DFB077300D661A9 /* NetworkSync.swift */,
				017C36DD2DFB077300D661A9 /* PreviewEngine.swift */,
				017C36DF2DFB077300D661A9 /* SearchEngine.swift */,
				017C36E02DFB077300D661A9 /* SnapshotManager.swift */,
				017C36E12DFB077300D661A9 /* VersionControl.swift */,
			);
			path = AugmentCore;
			sourceTree = "<group>";
		};
		017C36E72DFB077300D661A9 /* AugmentFileSystem */ = {
			isa = PBXGroup;
			children = (
				017C36E32DFB077300D661A9 /* AugmentFileSystem.swift */,
				017C36E42DFB077300D661A9 /* AugmentFUSE.swift */,
				017C36E52DFB077300D661A9 /* FileOperationInterceptor.swift */,
			);
			path = AugmentFileSystem;
			sourceTree = "<group>";
		};
		8A123456123456780000000C /* Products */ = {
			isa = PBXGroup;
			children = (
				8A1234561234567800000009 /* Augment.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8A123456123456780000000D /* Augment */ = {
			isa = PBXGroup;
			children = (
				8A1234561234567800000002 /* AugmentApp.swift */,
				8A1234561234567800000004 /* ContentView.swift */,
				8A123456123456780000002A /* SpaceDetailView.swift */,
				8A123456123456780000002B /* SearchView.swift */,
				8A123456123456780000002C /* ConflictResolutionView.swift */,
				8A123456123456780000002D /* VersionBrowser.swift */,

				8A1234561234567800000006 /* Assets.xcassets */,
				8A123456123456780000000A /* Augment.entitlements */,
				8A123456123456780000001F /* Info.plist */,
				8A123456123456780000000E /* Preview Content */,
			);
			path = Augment;
			sourceTree = "<group>";
		};
		8A123456123456780000000E /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				8A1234561234567800000008 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		8A123456123456780000000F = {
			isa = PBXGroup;
			children = (
				017C36E22DFB077300D661A9 /* AugmentCore */,
				017C36E72DFB077300D661A9 /* AugmentFileSystem */,
				8A123456123456780000000D /* Augment */,
				8A123456123456780000000C /* Products */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8A1234561234567800000010 /* Augment */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8A1234561234567800000011 /* Build configuration list for PBXNativeTarget "Augment" */;
			buildPhases = (
				8A1234561234567800000012 /* Sources */,
				8A123456123456780000000B /* Frameworks */,
				8A1234561234567800000013 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Augment;
			productName = Augment;
			productReference = 8A1234561234567800000009 /* Augment.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8A1234561234567800000014 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					8A1234561234567800000010 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = 8A1234561234567800000015 /* Build configuration list for PBXProject "Augment" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 8A123456123456780000000F;
			productRefGroup = 8A123456123456780000000C /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8A1234561234567800000010 /* Augment */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8A1234561234567800000013 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8A1234561234567800000007 /* Preview Assets.xcassets in Resources */,
				8A1234561234567800000005 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8A1234561234567800000012 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8A1234561234567800000003 /* ContentView.swift in Sources */,
				8A1234561234567800000001 /* AugmentApp.swift in Sources */,
				8A123456123456780000002E /* SpaceDetailView.swift in Sources */,
				8A123456123456780000002F /* SearchView.swift in Sources */,
				8A1234561234567800000030 /* ConflictResolutionView.swift in Sources */,
				8A1234561234567800000031 /* VersionBrowser.swift in Sources */,

				017C36E82DFB077300D661A9 /* BackupManager.swift in Sources */,
				017C36E92DFB077300D661A9 /* PreviewEngine.swift in Sources */,
				017C36EA2DFB077300D661A9 /* FileOperationInterceptor.swift in Sources */,
				017C36EB2DFB077300D661A9 /* AugmentFileSystem.swift in Sources */,
				017C36EC2DFB077300D661A9 /* VersionControl.swift in Sources */,
				017C36F62DFB0ECB00D661A9 /* FileType.swift in Sources */,
				017C36F72DFB0ECB00D661A9 /* AugmentSpace.swift in Sources */,
				017C36F92DFB0ECB00D661A9 /* FileItem.swift in Sources */,
				017C36ED2DFB077300D661A9 /* SearchEngine.swift in Sources */,
				017C36EE2DFB077300D661A9 /* AugmentFUSE.swift in Sources */,
				017C36EF2DFB077300D661A9 /* FileSystemMonitor.swift in Sources */,
				017C36F02DFB077300D661A9 /* ConflictResolution.swift in Sources */,
				017C36F12DFB077300D661A9 /* NetworkSync.swift in Sources */,
				017C36F22DFB077300D661A9 /* SnapshotManager.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		8A1234561234567800000016 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 2TKF278BYV;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		8A1234561234567800000017 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 2TKF278BYV;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		8A1234561234567800000018 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Augment/Augment.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"Augment/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = Augment/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.augment.Augment;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		8A1234561234567800000019 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Augment/Augment.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"Augment/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = Augment/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.augment.Augment;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8A1234561234567800000011 /* Build configuration list for PBXNativeTarget "Augment" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8A1234561234567800000018 /* Debug */,
				8A1234561234567800000019 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8A1234561234567800000015 /* Build configuration list for PBXProject "Augment" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8A1234561234567800000016 /* Debug */,
				8A1234561234567800000017 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8A1234561234567800000014 /* Project object */;
}
