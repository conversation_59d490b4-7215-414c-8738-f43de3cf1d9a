import SwiftUI

/// Error recovery dialog that guides users through error resolution
struct ErrorRecoveryView: View {
    @ObservedObject private var errorManager = ErrorRecoveryManager.shared
    @State private var selectedStrategy: RecoveryStrategy?
    @State private var showTechnicalDetails = false
    
    let error: RecoverableError
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerView
            
            Divider()
            
            // Main content
            ScrollView {
                VStack(spacing: 24) {
                    // Error description
                    errorDescriptionView
                    
                    // Recovery strategies
                    recoveryStrategiesView
                    
                    // Technical details (collapsible)
                    technicalDetailsView
                }
                .padding(24)
            }
            
            Divider()
            
            // Footer with action buttons
            footerView
        }
        .frame(width: 600, height: 500)
        .background(Color(NSColor.windowBackgroundColor))
    }
    
    // MARK: - Header
    
    private var headerView: some View {
        HStack(spacing: 16) {
            Image(systemName: error.category.icon)
                .font(.system(size: 32))
                .foregroundColor(.red)
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Error Occurred")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text(error.category.rawValue)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button("Close") {
                errorManager.dismissErrorDialog()
            }
            .buttonStyle(PlainButtonStyle())
            .foregroundColor(.secondary)
        }
        .padding(20)
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    // MARK: - Error Description
    
    private var errorDescriptionView: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("What happened?")
                .font(.headline)
            
            Text(error.userMessage)
                .font(.body)
                .foregroundColor(.primary)
                .fixedSize(horizontal: false, vertical: true)
            
            if let context = error.context {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Context:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                    
                    Text(context)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(8)
                        .background(Color(NSColor.controlBackgroundColor))
                        .cornerRadius(4)
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Recovery Strategies
    
    private var recoveryStrategiesView: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("How to fix it")
                .font(.headline)
            
            Text("Try one of these solutions:")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            VStack(spacing: 12) {
                ForEach(error.recoveryStrategies, id: \.self) { strategy in
                    RecoveryStrategyRow(
                        strategy: strategy,
                        isSelected: selectedStrategy == strategy,
                        isInProgress: errorManager.recoveryInProgress && selectedStrategy == strategy
                    ) {
                        selectedStrategy = strategy
                        errorManager.executeManualRecovery(strategy, for: error)
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Technical Details
    
    private var technicalDetailsView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Button(action: {
                showTechnicalDetails.toggle()
            }) {
                HStack {
                    Text("Technical Details")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Image(systemName: showTechnicalDetails ? "chevron.up" : "chevron.down")
                        .font(.caption)
                }
                .foregroundColor(.secondary)
            }
            .buttonStyle(PlainButtonStyle())
            
            if showTechnicalDetails {
                ScrollView {
                    Text(error.technicalDetails)
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.secondary)
                        .textSelection(.enabled)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .frame(maxHeight: 120)
                .padding(12)
                .background(Color(NSColor.controlBackgroundColor))
                .cornerRadius(8)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Footer
    
    private var footerView: some View {
        HStack(spacing: 16) {
            Button("Copy Error Details") {
                copyErrorDetails()
            }
            .buttonStyle(SecondaryButtonStyle())
            
            Spacer()
            
            Button("Ignore") {
                errorManager.dismissErrorDialog()
            }
            .buttonStyle(SecondaryButtonStyle())
            
            Button("Get Help") {
                openHelpForError()
            }
            .buttonStyle(PrimaryButtonStyle())
        }
        .padding(20)
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    // MARK: - Actions
    
    private func copyErrorDetails() {
        let details = """
        Error Category: \(error.category.rawValue)
        Timestamp: \(error.timestamp)
        Context: \(error.context ?? "None")
        
        User Message:
        \(error.userMessage)
        
        Technical Details:
        \(error.technicalDetails)
        """
        
        NSPasteboard.general.clearContents()
        NSPasteboard.general.setString(details, forType: .string)
    }
    
    private func openHelpForError() {
        let helpSystem = HelpSystem.shared
        helpSystem.showHelp(for: .troubleshooting)
        errorManager.dismissErrorDialog()
    }
}

// MARK: - Recovery Strategy Row

struct RecoveryStrategyRow: View {
    let strategy: RecoveryStrategy
    let isSelected: Bool
    let isInProgress: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Strategy icon
                Image(systemName: strategy.icon)
                    .font(.title3)
                    .foregroundColor(isSelected ? .white : .blue)
                    .frame(width: 24)
                
                // Strategy details
                VStack(alignment: .leading, spacing: 4) {
                    Text(strategy.rawValue)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(isSelected ? .white : .primary)
                    
                    Text(strategyDescription)
                        .font(.caption)
                        .foregroundColor(isSelected ? .white.opacity(0.8) : .secondary)
                }
                
                Spacer()
                
                // Progress indicator or status
                if isInProgress {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: isSelected ? .white : .blue))
                } else if strategy.isAutomatic {
                    Text("Auto")
                        .font(.caption2)
                        .fontWeight(.medium)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(isSelected ? Color.white.opacity(0.2) : Color.blue.opacity(0.1))
                        .foregroundColor(isSelected ? .white : .blue)
                        .cornerRadius(4)
                }
            }
            .padding(16)
            .background(isSelected ? Color.blue : Color(NSColor.controlBackgroundColor))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isSelected ? Color.blue : Color.gray.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isInProgress)
    }
    
    private var strategyDescription: String {
        switch strategy {
        case .retryOperation:
            return "Try the operation again"
        case .freeUpSpace:
            return "Clean up old files to free disk space"
        case .checkFilePermissions:
            return "Verify file access permissions"
        case .requestPermissions:
            return "Request necessary system permissions"
        case .runCleanup:
            return "Run automatic cleanup process"
        case .resetConfiguration:
            return "Reset settings to default values"
        case .restoreDefaults:
            return "Restore all preferences to defaults"
        case .checkNetworkConnection:
            return "Verify internet connectivity"
        case .recreateFile:
            return "Create a new version of the file"
        case .adjustStorageSettings:
            return "Modify storage limits and policies"
        case .checkSystemSettings:
            return "Review macOS system settings"
        case .runAsAdministrator:
            return "Restart with elevated privileges"
        case .workOffline:
            return "Continue without network access"
        case .reimportSettings:
            return "Import settings from backup"
        case .restartApplication:
            return "Restart Augment application"
        case .contactSupport:
            return "Get help from support team"
        }
    }
}

// MARK: - Error History View

struct ErrorHistoryView: View {
    @ObservedObject private var errorManager = ErrorRecoveryManager.shared
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Text("Error History")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("Clear Resolved") {
                    errorManager.clearResolvedErrors()
                }
                .buttonStyle(SecondaryButtonStyle())
            }
            .padding()
            
            Divider()
            
            // Error list
            List {
                ForEach(errorManager.getErrorHistory(), id: \.error.id) { entry in
                    ErrorHistoryRow(entry: entry)
                }
            }
            .listStyle(PlainListStyle())
        }
        .frame(width: 700, height: 500)
        .background(Color(NSColor.windowBackgroundColor))
    }
}

struct ErrorHistoryRow: View {
    let entry: ErrorHistoryEntry
    
    var body: some View {
        HStack(spacing: 16) {
            // Status indicator
            Circle()
                .fill(entry.isResolved ? Color.green : Color.red)
                .frame(width: 8, height: 8)
            
            // Error info
            VStack(alignment: .leading, spacing: 4) {
                Text(entry.error.category.rawValue)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(entry.error.userMessage)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            // Timestamp
            VStack(alignment: .trailing, spacing: 2) {
                Text(entry.occurredAt, style: .date)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Text(entry.occurredAt, style: .time)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 8)
    }
}

// MARK: - Preview

struct ErrorRecoveryView_Previews: PreviewProvider {
    static var previews: some View {
        let sampleError = RecoverableError(
            id: "sample",
            originalError: NSError(domain: "TestDomain", code: 1001, userInfo: nil),
            category: .fileSystem,
            context: "/Users/<USER>/Documents/file.txt",
            timestamp: Date(),
            recoveryStrategies: [.retryOperation, .checkFilePermissions, .contactSupport],
            userMessage: "There was a problem accessing the file. It might be locked by another application.",
            technicalDetails: "Error: File not accessible\nCode: 1001\nPath: /Users/<USER>/Documents/file.txt"
        )
        
        ErrorRecoveryView(error: sampleError)
    }
}
