import SwiftUI

/// Main help view that displays help content and search functionality
struct HelpView: View {
    @ObservedObject private var helpSystem = HelpSystem.shared
    @State private var selectedCategory: HelpCategory?
    @State private var selectedArticle: HelpArticle?
    
    var body: some View {
        NavigationView {
            // Sidebar with categories and search
            VStack(spacing: 0) {
                // Search bar
                searchSection
                
                Divider()
                
                // Categories list
                categoriesSection
            }
            .frame(minWidth: 250, maxWidth: 300)
            
            // Main content area
            contentSection
        }
        .frame(width: 900, height: 700)
        .navigationTitle("Augment Help")
        .toolbar {
            ToolbarItem(placement: .navigation) {
                Button("Close") {
                    helpSystem.hideHelp()
                }
            }
        }
        .onAppear {
            // Select first category by default
            if selectedCategory == nil {
                selectedCategory = .gettingStarted
            }
        }
    }
    
    // MARK: - Search Section
    
    private var searchSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search help articles...", text: $helpSystem.searchQuery)
                    .textFieldStyle(PlainTextFieldStyle())
                    .onChange(of: helpSystem.searchQuery) { query in
                        helpSystem.searchHelp(query)
                    }
                
                if !helpSystem.searchQuery.isEmpty {
                    Button(action: {
                        helpSystem.searchQuery = ""
                        helpSystem.searchHelp("")
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(NSColor.controlBackgroundColor))
            .cornerRadius(8)
            
            // Search results
            if !helpSystem.searchQuery.isEmpty {
                searchResultsSection
            }
        }
        .padding()
    }
    
    private var searchResultsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Search Results (\(helpSystem.searchResults.count))")
                .font(.headline)
                .foregroundColor(.secondary)
            
            if helpSystem.searchResults.isEmpty {
                Text("No articles found")
                    .foregroundColor(.secondary)
                    .italic()
            } else {
                ForEach(helpSystem.searchResults) { article in
                    Button(action: {
                        selectedArticle = article
                        selectedCategory = article.category
                    }) {
                        HStack {
                            Image(systemName: article.category.icon)
                                .foregroundColor(.blue)
                                .frame(width: 16)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text(article.title)
                                    .font(.subheadline)
                                    .foregroundColor(.primary)
                                
                                Text(article.category.rawValue)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                        }
                        .padding(.vertical, 4)
                        .padding(.horizontal, 8)
                        .background(selectedArticle?.id == article.id ? Color.blue.opacity(0.1) : Color.clear)
                        .cornerRadius(4)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
    
    // MARK: - Categories Section
    
    private var categoriesSection: some View {
        VStack(alignment: .leading, spacing: 0) {
            if helpSystem.searchQuery.isEmpty {
                Text("Help Topics")
                    .font(.headline)
                    .foregroundColor(.secondary)
                    .padding(.horizontal)
                    .padding(.top)
                
                List(selection: $selectedCategory) {
                    ForEach(helpSystem.getAllCategories(), id: \.self) { category in
                        CategoryRow(category: category, isSelected: selectedCategory == category)
                            .tag(category)
                    }
                }
                .listStyle(SidebarListStyle())
                .onChange(of: selectedCategory) { category in
                    if let category = category {
                        // Select first article in category
                        let articles = helpSystem.getArticles(for: category)
                        selectedArticle = articles.first
                    }
                }
            }
        }
    }
    
    // MARK: - Content Section
    
    private var contentSection: some View {
        Group {
            if let article = selectedArticle {
                ArticleView(article: article)
            } else if let category = selectedCategory {
                CategoryOverview(category: category)
            } else {
                WelcomeView()
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Category Row

struct CategoryRow: View {
    let category: HelpCategory
    let isSelected: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: category.icon)
                .foregroundColor(isSelected ? .white : .blue)
                .frame(width: 20)
            
            Text(category.rawValue)
                .foregroundColor(isSelected ? .white : .primary)
            
            Spacer()
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(isSelected ? Color.blue : Color.clear)
        .cornerRadius(6)
    }
}

// MARK: - Article View

struct ArticleView: View {
    let article: HelpArticle
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Article header
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: article.category.icon)
                            .foregroundColor(.blue)
                        
                        Text(article.category.rawValue)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Text(article.title)
                        .font(.largeTitle)
                        .fontWeight(.bold)
                }
                
                Divider()
                
                // Article content
                Text(article.content)
                    .font(.body)
                    .lineSpacing(4)
                    .textSelection(.enabled)
                
                Spacer()
            }
            .padding(30)
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .background(Color(NSColor.textBackgroundColor))
    }
}

// MARK: - Category Overview

struct CategoryOverview: View {
    let category: HelpCategory
    
    var body: some View {
        VStack(spacing: 30) {
            VStack(spacing: 16) {
                Image(systemName: category.icon)
                    .font(.system(size: 60))
                    .foregroundColor(.blue)
                
                Text(category.rawValue)
                    .font(.largeTitle)
                    .fontWeight(.bold)
            }
            
            VStack(spacing: 12) {
                Text("Articles in this category:")
                    .font(.headline)
                    .foregroundColor(.secondary)
                
                let articles = HelpSystem.shared.getArticles(for: category)
                ForEach(articles) { article in
                    Button(article.title) {
                        // This would select the article
                    }
                    .buttonStyle(PlainButtonStyle())
                    .foregroundColor(.blue)
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(NSColor.textBackgroundColor))
    }
}

// MARK: - Welcome View

struct WelcomeView: View {
    var body: some View {
        VStack(spacing: 30) {
            Image(systemName: "questionmark.circle")
                .font(.system(size: 80))
                .foregroundColor(.blue)
            
            VStack(spacing: 16) {
                Text("Welcome to Augment Help")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Select a topic from the sidebar to get started, or use the search bar to find specific information.")
                    .font(.title3)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .frame(maxWidth: 500)
            }
            
            VStack(spacing: 12) {
                Text("Popular topics:")
                    .font(.headline)
                    .foregroundColor(.secondary)
                
                HStack(spacing: 16) {
                    QuickHelpButton(title: "Getting Started", icon: "play.circle") {
                        HelpSystem.shared.showHelp(for: .gettingStarted)
                    }
                    
                    QuickHelpButton(title: "Creating Spaces", icon: "folder.badge.plus") {
                        HelpSystem.shared.showHelp(for: .creatingSpaces)
                    }
                    
                    QuickHelpButton(title: "Version History", icon: "clock.arrow.circlepath") {
                        HelpSystem.shared.showHelp(for: .versionHistory)
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(NSColor.textBackgroundColor))
    }
}

// MARK: - Quick Help Button

struct QuickHelpButton: View {
    let title: String
    let icon: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title)
                    .foregroundColor(.blue)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
            }
            .frame(width: 100, height: 80)
            .background(Color(NSColor.controlBackgroundColor))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview

struct HelpView_Previews: PreviewProvider {
    static var previews: some View {
        HelpView()
    }
}
