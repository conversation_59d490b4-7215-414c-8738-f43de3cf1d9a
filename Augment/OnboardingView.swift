import Foundation
import SwiftUI

/// Interactive onboarding system for first-time Augment users
/// Provides step-by-step guidance through app features and initial setup
struct OnboardingView: View {
    @State private var currentStep = 0
    @State private var isCompleted = false
    @State private var isCreatingSpace = false
    @State private var newSpaceName = ""
    @State private var newSpaceLocation = ""
    @State private var selectedDirectory: URL?
    @State private var demoFileCreated = false

    @Environment(\.presentationMode) var presentationMode

    private let steps: [OnboardingStep] = [
        .welcome,
        .valueProposition,
        .createFirstSpace,
        .demoVersioning,
        .completion,
    ]

    var body: some View {
        VStack(spacing: 0) {
            // Header with progress
            headerView

            // Main content area
            ZStack {
                Color(NSColor.windowBackgroundColor)

                VStack(spacing: 30) {
                    // Progress indicator
                    ProgressView(value: Double(currentStep), total: Double(steps.count - 1))
                        .progressViewStyle(LinearProgressViewStyle())
                        .frame(maxWidth: 400)

                    // Step content
                    stepContentView

                    Spacer()

                    // Navigation buttons
                    navigationButtonsView
                }
                .padding(40)
            }
        }
        .frame(width: 800, height: 600)
        .background(Color(NSColor.windowBackgroundColor))
    }

    // MARK: - Header View

    private var headerView: some View {
        HStack {
            Image(systemName: "clock.arrow.circlepath")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 32, height: 32)
                .foregroundColor(.blue)

            Text("Welcome to Augment")
                .font(.title2)
                .fontWeight(.semibold)

            Spacer()

            Button("Skip") {
                completeOnboarding()
            }
            .foregroundColor(.secondary)
        }
        .padding(.horizontal, 30)
        .padding(.vertical, 20)
        .background(Color(NSColor.controlBackgroundColor))
    }

    // MARK: - Step Content

    @ViewBuilder
    private var stepContentView: some View {
        switch steps[currentStep] {
        case .welcome:
            WelcomeStepView()
        case .valueProposition:
            ValuePropositionStepView()
        case .createFirstSpace:
            CreateSpaceStepView(
                spaceName: $newSpaceName,
                spaceLocation: $newSpaceLocation,
                selectedDirectory: $selectedDirectory,
                isCreatingSpace: $isCreatingSpace
            )
        case .demoVersioning:
            DemoVersioningStepView(demoFileCreated: $demoFileCreated)
        case .completion:
            CompletionStepView()
        }
    }

    // MARK: - Navigation Buttons

    private var navigationButtonsView: some View {
        HStack(spacing: 20) {
            if currentStep > 0 {
                Button("Previous") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        currentStep -= 1
                    }
                }
                .buttonStyle(SecondaryButtonStyle())
            }

            Spacer()

            Button(nextButtonTitle) {
                handleNextButton()
            }
            .buttonStyle(PrimaryButtonStyle())
            .disabled(isNextButtonDisabled)
        }
        .frame(maxWidth: 400)
    }

    private var nextButtonTitle: String {
        switch steps[currentStep] {
        case .welcome:
            return "Get Started"
        case .valueProposition:
            return "Continue"
        case .createFirstSpace:
            return isCreatingSpace ? "Creating..." : "Create Space"
        case .demoVersioning:
            return "Try It Out"
        case .completion:
            return "Start Using Augment"
        }
    }

    private var isNextButtonDisabled: Bool {
        switch steps[currentStep] {
        case .createFirstSpace:
            return newSpaceName.isEmpty || selectedDirectory == nil || isCreatingSpace
        case .demoVersioning:
            return false
        default:
            return false
        }
    }

    // MARK: - Actions

    private func handleNextButton() {
        switch steps[currentStep] {
        case .createFirstSpace:
            createFirstSpace()
        case .demoVersioning:
            createDemoFile()
        case .completion:
            completeOnboarding()
        default:
            moveToNextStep()
        }
    }

    private func moveToNextStep() {
        withAnimation(.easeInOut(duration: 0.3)) {
            if currentStep < steps.count - 1 {
                currentStep += 1
            } else {
                completeOnboarding()
            }
        }
    }

    private func createFirstSpace() {
        guard let directory = selectedDirectory else { return }

        isCreatingSpace = true

        DispatchQueue.global(qos: .userInitiated).async {
            let fileSystem = DependencyContainer.shared.augmentFileSystem()

            if let space = fileSystem.createSpace(name: newSpaceName, path: directory) {
                DispatchQueue.main.async {
                    self.isCreatingSpace = false
                    self.moveToNextStep()
                }
            } else {
                DispatchQueue.main.async {
                    self.isCreatingSpace = false
                    // TODO: Show error message
                }
            }
        }
    }

    private func createDemoFile() {
        guard let directory = selectedDirectory else {
            moveToNextStep()
            return
        }

        let demoFile = directory.appendingPathComponent("Welcome to Augment.txt")
        let demoContent = """
            Welcome to Augment!

            This is a demo file to show you how version control works.

            Try editing this file and saving it - Augment will automatically create a version for you.
            You can then view the version history and restore previous versions at any time.

            Key Features:
            • Automatic version creation on file changes
            • Complete version history for all files
            • Easy restoration of previous versions
            • Storage management and cleanup
            • Search across all versions

            Get started by editing this file!
            """

        do {
            try demoContent.write(to: demoFile, atomically: true, encoding: .utf8)
            demoFileCreated = true

            // Open the file in the default editor
            NSWorkspace.shared.open(demoFile)

            moveToNextStep()
        } catch {
            // If file creation fails, just move to next step
            moveToNextStep()
        }
    }

    private func completeOnboarding() {
        UserDefaults.standard.set(true, forKey: "hasCompletedOnboarding")
        UserDefaults.standard.set(Date(), forKey: "onboardingCompletedDate")

        // Track onboarding completion
        let logger = AugmentLogger.shared
        logger.info("User completed onboarding", category: .general)

        presentationMode.wrappedValue.dismiss()
    }
}

// MARK: - Onboarding Steps

enum OnboardingStep: CaseIterable {
    case welcome
    case valueProposition
    case createFirstSpace
    case demoVersioning
    case completion

    var title: String {
        switch self {
        case .welcome:
            return "Welcome to Augment"
        case .valueProposition:
            return "Never Lose Work Again"
        case .createFirstSpace:
            return "Create Your First Space"
        case .demoVersioning:
            return "Try Version Control"
        case .completion:
            return "You're All Set!"
        }
    }
}

// MARK: - Button Styles

struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.blue)
            .cornerRadius(8)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .foregroundColor(.primary)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color(NSColor.controlBackgroundColor))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Step Views

struct WelcomeStepView: View {
    var body: some View {
        VStack(spacing: 30) {
            Image(systemName: "clock.arrow.circlepath")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 120, height: 120)
                .foregroundColor(.blue)

            VStack(spacing: 16) {
                Text("Welcome to Augment")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                Text("The intelligent file versioning system for macOS")
                    .font(.title3)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            VStack(spacing: 12) {
                FeatureRow(
                    icon: "clock.arrow.circlepath", title: "Automatic Versioning",
                    description: "Never lose work again with automatic file version tracking")
                FeatureRow(
                    icon: "magnifyingglass", title: "Powerful Search",
                    description: "Find any version of any file instantly")
                FeatureRow(
                    icon: "arrow.clockwise", title: "Easy Recovery",
                    description: "Restore previous versions with a single click")
            }
            .padding(.top, 20)
        }
        .frame(maxWidth: 500)
    }
}

struct ValuePropositionStepView: View {
    var body: some View {
        VStack(spacing: 40) {
            VStack(spacing: 16) {
                Text("Never Lose Work Again")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                Text(
                    "Augment automatically saves every version of your files, so you can focus on creating without worrying about losing your work."
                )
                .font(.title3)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .frame(maxWidth: 600)
            }

            HStack(spacing: 40) {
                ProblemSolutionCard(
                    title: "Before Augment",
                    items: [
                        "Manual file naming (final_v2_FINAL.doc)",
                        "Lost work from crashes or mistakes",
                        "No way to compare versions",
                        "Cluttered folders with duplicates",
                    ],
                    color: .red
                )

                ProblemSolutionCard(
                    title: "With Augment",
                    items: [
                        "Automatic version tracking",
                        "Complete version history",
                        "Easy version comparison",
                        "Clean, organized workspace",
                    ],
                    color: .green
                )
            }
        }
        .frame(maxWidth: 800)
    }
}

struct CreateSpaceStepView: View {
    @Binding var spaceName: String
    @Binding var spaceLocation: String
    @Binding var selectedDirectory: URL?
    @Binding var isCreatingSpace: Bool

    var body: some View {
        VStack(spacing: 30) {
            VStack(spacing: 16) {
                Text("Create Your First Space")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                Text(
                    "A space is a folder where Augment will track file versions. Choose a folder you work with regularly."
                )
                .font(.title3)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .frame(maxWidth: 600)
            }

            VStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Space Name")
                        .font(.headline)

                    TextField("Enter a name for your space", text: $spaceName)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(maxWidth: 400)
                }

                VStack(alignment: .leading, spacing: 8) {
                    Text("Folder Location")
                        .font(.headline)

                    HStack {
                        TextField("Choose a folder", text: $spaceLocation)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .disabled(true)

                        Button("Browse...") {
                            selectDirectory()
                        }
                        .buttonStyle(SecondaryButtonStyle())
                    }
                    .frame(maxWidth: 400)
                }

                if selectedDirectory != nil {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                        Text("Folder selected successfully")
                            .foregroundColor(.secondary)
                    }
                }
            }

            VStack(spacing: 12) {
                Text("Recommended folders:")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                HStack(spacing: 16) {
                    QuickSelectButton(title: "Documents", icon: "doc.folder") {
                        selectDocumentsFolder()
                    }
                    QuickSelectButton(title: "Desktop", icon: "desktopcomputer") {
                        selectDesktopFolder()
                    }
                    QuickSelectButton(title: "Projects", icon: "folder") {
                        selectProjectsFolder()
                    }
                }
            }
        }
        .frame(maxWidth: 600)
    }

    private func selectDirectory() {
        let panel = NSOpenPanel()
        panel.canChooseFiles = false
        panel.canChooseDirectories = true
        panel.allowsMultipleSelection = false
        panel.title = "Select Folder for Augment Space"
        panel.message = "Choose a folder where you want Augment to track file versions"

        if panel.runModal() == .OK {
            if let url = panel.url {
                selectedDirectory = url
                spaceLocation = url.path
                if spaceName.isEmpty {
                    spaceName = url.lastPathComponent
                }
            }
        }
    }

    private func selectDocumentsFolder() {
        if let documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
            .first
        {
            selectedDirectory = documentsURL
            spaceLocation = documentsURL.path
            spaceName = "Documents"
        }
    }

    private func selectDesktopFolder() {
        if let desktopURL = FileManager.default.urls(for: .desktopDirectory, in: .userDomainMask)
            .first
        {
            selectedDirectory = desktopURL
            spaceLocation = desktopURL.path
            spaceName = "Desktop"
        }
    }

    private func selectProjectsFolder() {
        if let homeURL = FileManager.default.homeDirectoryForCurrentUser.appendingPathComponent(
            "Projects", isDirectory: true) as URL?
        {
            // Create Projects folder if it doesn't exist
            try? FileManager.default.createDirectory(at: homeURL, withIntermediateDirectories: true)
            selectedDirectory = homeURL
            spaceLocation = homeURL.path
            spaceName = "Projects"
        }
    }
}

struct DemoVersioningStepView: View {
    @Binding var demoFileCreated: Bool

    var body: some View {
        VStack(spacing: 30) {
            VStack(spacing: 16) {
                Text("Try Version Control")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                Text("Let's create a demo file so you can see how Augment works in action.")
                    .font(.title3)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .frame(maxWidth: 600)
            }

            VStack(spacing: 20) {
                DemoStepCard(
                    number: "1",
                    title: "Create Demo File",
                    description: "We'll create a sample text file in your space",
                    isCompleted: false
                )

                DemoStepCard(
                    number: "2",
                    title: "Edit the File",
                    description: "Make some changes and save the file",
                    isCompleted: false
                )

                DemoStepCard(
                    number: "3",
                    title: "View Version History",
                    description: "See how Augment automatically tracked your changes",
                    isCompleted: false
                )
            }

            if demoFileCreated {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("Demo file created! It should open in your default text editor.")
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.green.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .frame(maxWidth: 600)
    }
}

struct CompletionStepView: View {
    var body: some View {
        VStack(spacing: 30) {
            Image(systemName: "checkmark.circle.fill")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 120, height: 120)
                .foregroundColor(.green)

            VStack(spacing: 16) {
                Text("You're All Set!")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                Text("Augment is now protecting your files. Here's what happens next:")
                    .font(.title3)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .frame(maxWidth: 600)
            }

            VStack(spacing: 16) {
                NextStepRow(
                    icon: "clock.arrow.circlepath",
                    title: "Automatic Protection",
                    description: "Augment will automatically create versions when you modify files"
                )

                NextStepRow(
                    icon: "magnifyingglass",
                    title: "Find Anything",
                    description: "Use the search feature to find any version of any file"
                )

                NextStepRow(
                    icon: "questionmark.circle",
                    title: "Get Help",
                    description: "Access help anytime from the menu or press Cmd+? for shortcuts"
                )
            }
            .padding(.top, 20)
        }
        .frame(maxWidth: 600)
    }
}

// MARK: - Helper Components

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .frame(width: 24, height: 24)
                .foregroundColor(.blue)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}

struct ProblemSolutionCard: View {
    let title: String
    let items: [String]
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(title)
                .font(.headline)
                .foregroundColor(color)

            VStack(alignment: .leading, spacing: 8) {
                ForEach(items, id: \.self) { item in
                    HStack(alignment: .top, spacing: 8) {
                        Image(
                            systemName: color == .red
                                ? "xmark.circle.fill" : "checkmark.circle.fill"
                        )
                        .foregroundColor(color)
                        .frame(width: 16, height: 16)

                        Text(item)
                            .font(.subheadline)
                            .multilineTextAlignment(.leading)
                    }
                }
            }
        }
        .padding(20)
        .frame(maxWidth: 300, alignment: .topLeading)
        .background(color.opacity(0.1))
        .cornerRadius(12)
    }
}

struct QuickSelectButton: View {
    let title: String
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.blue)

                Text(title)
                    .font(.caption)
                    .foregroundColor(.primary)
            }
            .frame(width: 80, height: 60)
            .background(Color(NSColor.controlBackgroundColor))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct DemoStepCard: View {
    let number: String
    let title: String
    let description: String
    let isCompleted: Bool

    var body: some View {
        HStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(isCompleted ? Color.green : Color.blue)
                    .frame(width: 32, height: 32)

                if isCompleted {
                    Image(systemName: "checkmark")
                        .foregroundColor(.white)
                        .font(.system(size: 14, weight: .bold))
                } else {
                    Text(number)
                        .foregroundColor(.white)
                        .font(.system(size: 14, weight: .bold))
                }
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .padding(16)
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(isCompleted ? Color.green : Color.gray.opacity(0.3), lineWidth: 1)
        )
    }
}

struct NextStepRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .frame(width: 24, height: 24)
                .foregroundColor(.blue)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}

// MARK: - Preview

struct OnboardingView_Previews: PreviewProvider {
    static var previews: some View {
        OnboardingView()
    }
}
