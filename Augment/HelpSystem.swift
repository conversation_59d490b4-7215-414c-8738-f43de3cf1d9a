import SwiftUI
import Foundation

/// Comprehensive help system for Augment application
/// Provides contextual help, tutorials, and documentation
public class HelpSystem: ObservableObject {
    
    // MARK: - Singleton
    public static let shared = HelpSystem()
    
    // MARK: - Published Properties
    @Published public var isShowingHelp = false
    @Published public var currentHelpTopic: HelpTopic?
    @Published public var searchQuery = ""
    @Published public var searchResults: [HelpArticle] = []
    
    // MARK: - Dependencies
    private let logger = AugmentLogger.shared
    
    // MARK: - Help Content
    private let helpArticles: [HelpArticle] = [
        HelpArticle(
            id: "getting-started",
            title: "Getting Started with Augment",
            category: .gettingStarted,
            content: """
            Welcome to Augment! This guide will help you get started with automatic file versioning.
            
            ## What is Augment?
            Augment is an intelligent file versioning system that automatically saves every version of your files, so you never lose work again.
            
            ## Key Concepts
            - **Spaces**: Folders where Augment tracks file versions
            - **Versions**: Automatic snapshots of your files when they change
            - **Version History**: Complete timeline of all file changes
            
            ## First Steps
            1. Create your first space by selecting a folder
            2. Start working with files in that folder
            3. Augment automatically creates versions when files change
            4. View version history anytime by right-clicking files
            """,
            keywords: ["getting started", "introduction", "basics", "first time", "setup"]
        ),
        
        HelpArticle(
            id: "creating-spaces",
            title: "Creating and Managing Spaces",
            category: .creatingSpaces,
            content: """
            ## Creating a New Space
            1. Click the "+" button in the sidebar
            2. Choose a name for your space
            3. Select the folder you want to track
            4. Click "Create Space"
            
            ## Space Settings
            Each space has its own settings:
            - **Auto-versioning**: Enable/disable automatic version creation
            - **Storage limits**: Set maximum storage for versions
            - **Cleanup policies**: Configure automatic cleanup of old versions
            
            ## Best Practices
            - Use descriptive names for your spaces
            - Choose folders you actively work with
            - Set appropriate storage limits based on your needs
            - Enable cleanup policies to manage disk usage
            """,
            keywords: ["spaces", "create", "manage", "folder", "setup", "configuration"]
        ),
        
        HelpArticle(
            id: "version-history",
            title: "Understanding Version History",
            category: .versionHistory,
            content: """
            ## Viewing Version History
            - Right-click any file and select "View Version History"
            - Use the version browser to see all versions
            - Compare different versions side by side
            
            ## Version Information
            Each version includes:
            - Timestamp when the version was created
            - File size and content hash
            - Automatic comment describing the change
            
            ## Version Timeline
            - Versions are displayed chronologically
            - Recent versions appear at the top
            - Use the search feature to find specific versions
            
            ## Version Comparison
            - Select two versions to compare
            - View differences highlighted in the comparison view
            - Understand exactly what changed between versions
            """,
            keywords: ["version", "history", "timeline", "compare", "view", "browse"]
        ),
        
        HelpArticle(
            id: "file-restoration",
            title: "Restoring Previous Versions",
            category: .fileRestoration,
            content: """
            ## How to Restore Files
            1. Open the version history for the file
            2. Select the version you want to restore
            3. Click "Restore This Version"
            4. Choose whether to replace the current file or create a copy
            
            ## Restoration Options
            - **Replace**: Overwrites the current file with the selected version
            - **Copy**: Creates a new file with the restored content
            - **Preview**: View the content before restoring
            
            ## Safety Features
            - Current version is automatically backed up before restoration
            - You can undo restorations by restoring the backup
            - All restoration actions are logged for audit purposes
            
            ## Bulk Restoration
            - Restore multiple files to a specific point in time
            - Use snapshots to restore entire folder states
            - Filter files by type or modification date
            """,
            keywords: ["restore", "recovery", "backup", "undo", "previous", "version"]
        ),
        
        HelpArticle(
            id: "troubleshooting",
            title: "Troubleshooting Common Issues",
            category: .troubleshooting,
            content: """
            ## Common Issues and Solutions
            
            ### Files Not Being Versioned
            - Check that the file is in a monitored space
            - Verify auto-versioning is enabled for the space
            - Ensure the file type is not excluded
            
            ### Storage Warnings
            - Review storage usage in preferences
            - Enable automatic cleanup policies
            - Manually clean up old versions if needed
            
            ### Performance Issues
            - Check available disk space
            - Reduce the number of monitored files
            - Adjust monitoring frequency in preferences
            
            ### Permission Errors
            - Ensure Augment has full disk access
            - Check folder permissions for your spaces
            - Run Augment with administrator privileges if needed
            
            ## Getting More Help
            - Check the logs for detailed error information
            - Contact support with specific error messages
            - Use the built-in diagnostic tools
            """,
            keywords: ["troubleshooting", "problems", "issues", "errors", "help", "support"]
        )
    ]
    
    // MARK: - Initialization
    
    private init() {
        logger.info("Help system initialized", category: .general)
    }
    
    // MARK: - Public Interface
    
    /// Shows help for a specific topic
    /// - Parameter topic: The help topic to display
    public func showHelp(for topic: HelpTopic) {
        currentHelpTopic = topic
        isShowingHelp = true
        
        logger.info("Showing help for topic: \(topic)", category: .general)
    }
    
    /// Shows contextual help for a specific view or feature
    /// - Parameter context: The context identifier
    public func showContextualHelp(for context: String) {
        let topic = HelpTopic.contextualHelp(context)
        showHelp(for: topic)
    }
    
    /// Searches help articles for the given query
    /// - Parameter query: The search query
    public func searchHelp(_ query: String) {
        searchQuery = query
        
        if query.isEmpty {
            searchResults = []
            return
        }
        
        let lowercaseQuery = query.lowercased()
        searchResults = helpArticles.filter { article in
            article.title.lowercased().contains(lowercaseQuery) ||
            article.content.lowercased().contains(lowercaseQuery) ||
            article.keywords.contains { $0.lowercased().contains(lowercaseQuery) }
        }
        
        logger.info("Help search for '\(query)' returned \(searchResults.count) results", category: .general)
    }
    
    /// Gets all articles for a specific category
    /// - Parameter category: The help category
    /// - Returns: Array of help articles
    public func getArticles(for category: HelpCategory) -> [HelpArticle] {
        return helpArticles.filter { $0.category == category }
    }
    
    /// Gets all available help categories
    /// - Returns: Array of help categories
    public func getAllCategories() -> [HelpCategory] {
        return HelpCategory.allCases
    }
    
    /// Hides the help system
    public func hideHelp() {
        isShowingHelp = false
        currentHelpTopic = nil
        
        logger.info("Help system hidden", category: .general)
    }
}

// MARK: - Help Data Models

/// Available help topics
public enum HelpTopic: Equatable {
    case gettingStarted
    case creatingSpaces
    case versionHistory
    case fileRestoration
    case troubleshooting
    case contextualHelp(String)
    
    var title: String {
        switch self {
        case .gettingStarted:
            return "Getting Started"
        case .creatingSpaces:
            return "Creating Spaces"
        case .versionHistory:
            return "Version History"
        case .fileRestoration:
            return "File Restoration"
        case .troubleshooting:
            return "Troubleshooting"
        case .contextualHelp(let context):
            return "Help: \(context)"
        }
    }
}

/// Help article categories
public enum HelpCategory: String, CaseIterable {
    case gettingStarted = "Getting Started"
    case creatingSpaces = "Creating Spaces"
    case versionHistory = "Version History"
    case fileRestoration = "File Restoration"
    case troubleshooting = "Troubleshooting"
    case advanced = "Advanced"
    
    var icon: String {
        switch self {
        case .gettingStarted:
            return "play.circle"
        case .creatingSpaces:
            return "folder.badge.plus"
        case .versionHistory:
            return "clock.arrow.circlepath"
        case .fileRestoration:
            return "arrow.clockwise"
        case .troubleshooting:
            return "wrench.and.screwdriver"
        case .advanced:
            return "gearshape.2"
        }
    }
}

/// Individual help article
public struct HelpArticle: Identifiable, Hashable {
    public let id: String
    public let title: String
    public let category: HelpCategory
    public let content: String
    public let keywords: [String]
    
    public func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    public static func == (lhs: HelpArticle, rhs: HelpArticle) -> Bool {
        return lhs.id == rhs.id
    }
}
