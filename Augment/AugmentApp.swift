import Foundation
import SwiftUI

@main
struct AugmentApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var helpSystem = HelpSystem.shared
    @StateObject private var errorRecoveryManager = ErrorRecoveryManager.shared
    @State private var isShowingPreferences = false
    @State private var showingOnboarding = false
    @State private var fileToOpen: String?
    @State private var openVersionHistory = false
    @State private var openRestoreVersion = false

    init() {
        // Parse command-line arguments
        let arguments = CommandLine.arguments

        for (index, argument) in arguments.enumerated() {
            if argument == "--open-version-history" && index + 1 < arguments.count {
                fileToOpen = arguments[index + 1]
                openVersionHistory = true
            } else if argument == "--restore-version" && index + 1 < arguments.count {
                fileToOpen = arguments[index + 1]
                openRestoreVersion = true
            }
        }
    }

    private func openPreferences() {
        print("🚨 PREFERENCES CALLED FROM AUGMENTAPP!")
        print("🔧 DEBUG: openPreferences() called in AugmentApp")

        // Use NSApp to open the preferences window
        NSApp.activate(ignoringOtherApps: true)

        // Try to find existing preferences window
        for window in NSApp.windows {
            print("🔧 DEBUG: Found window with title: '\(window.title)'")
            if window.title == "Augment Preferences" {
                print("🔧 DEBUG: Found existing preferences window, bringing to front")
                window.makeKeyAndOrderFront(nil)
                return
            }
        }

        print("🔧 DEBUG: Creating new preferences window")

        // If no window found, create a new preferences window manually
        let preferencesWindow = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 800, height: 700),
            styleMask: [.titled, .closable, .miniaturizable, .resizable],
            backing: .buffered,
            defer: false
        )
        preferencesWindow.title = "Augment Preferences"
        preferencesWindow.isReleasedWhenClosed = false
        preferencesWindow.minSize = NSSize(width: 700, height: 600)
        preferencesWindow.maxSize = NSSize(width: 1200, height: 1000)

        print("🔧 DEBUG: Creating SimplePreferencesView")

        // Create the actual Storage Management preferences view
        let preferencesView = SimplePreferencesView()

        // Create the hosting view with our preferences
        let hostingView = NSHostingView(rootView: preferencesView)
        hostingView.frame = NSRect(x: 0, y: 0, width: 800, height: 700)

        preferencesWindow.contentView = hostingView
        preferencesWindow.center()
        preferencesWindow.makeKeyAndOrderFront(nil)

        print("🔧 DEBUG: Preferences window created and displayed")

        // Note: Delegate removed to prevent deallocation warning
        // preferencesWindow.delegate = PreferencesWindowDelegate()
    }

    /// Checks if this is the first launch and shows onboarding if needed
    private func checkForFirstLaunch() {
        let hasCompletedOnboarding = UserDefaults.standard.bool(forKey: "hasCompletedOnboarding")

        if !hasCompletedOnboarding {
            // Delay showing onboarding slightly to let the main window appear first
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                showingOnboarding = true
            }
        }
    }

    var body: some Scene {
        WindowGroup {
            ContentView(
                fileToOpen: fileToOpen,
                openVersionHistory: openVersionHistory,
                openRestoreVersion: openRestoreVersion
            )
            .frame(minWidth: 800, minHeight: 600)
            .onAppear {
                checkForFirstLaunch()
            }
            .sheet(isPresented: $showingOnboarding) {
                OnboardingView()
            }
            .sheet(isPresented: $helpSystem.isShowingHelp) {
                HelpView()
            }
            .sheet(isPresented: $errorRecoveryManager.isShowingErrorDialog) {
                if let error = errorRecoveryManager.currentError {
                    ErrorRecoveryView(error: error)
                }
            }
        }
        .windowStyle(HiddenTitleBarWindowStyle())
        .commands {
            CommandGroup(replacing: .appSettings) {
                Button("Preferences...") {
                    openPreferences()
                }
                .keyboardShortcut(",", modifiers: .command)
            }

            CommandGroup(replacing: .help) {
                Button("Augment Help") {
                    helpSystem.showHelp(for: .gettingStarted)
                }
                .keyboardShortcut("?", modifiers: .command)

                Button("Show Onboarding") {
                    showingOnboarding = true
                }
            }
        }

        // Add menu bar extra
        MenuBarExtra("Augment", systemImage: "clock.arrow.circlepath") {
            MenuBarView(isShowingPreferences: $isShowingPreferences)
        }

        // Note: Preferences window is created manually in openPreferences() function
        // to ensure proper display of the interface
    }
}

class AppDelegate: NSObject, NSApplicationDelegate {
    var statusBarItem: NSStatusItem?
    private var snapshotScheduler: Timer?

    func applicationDidFinishLaunching(_ notification: Notification) {
        // SIMPLIFIED VERSION: Disable auto versioning features
        print("🔧 AugmentApp: Starting in SIMPLIFIED mode (no auto versioning)")

        // Only register for URL events - disable auto monitoring
        NSAppleEventManager.shared().setEventHandler(
            self,
            andSelector: #selector(handleURLEvent(_:withReplyEvent:)),
            forEventClass: AEEventClass(kInternetEventClass),
            andEventID: AEEventID(kAEGetURL)
        )

        print("✅ AugmentApp: Simplified initialization completed")
    }

    @objc func handleURLEvent(
        _ event: NSAppleEventDescriptor, withReplyEvent replyEvent: NSAppleEventDescriptor
    ) {
        guard let urlString = event.paramDescriptor(forKeyword: keyDirectObject)?.stringValue,
            let url = URL(string: urlString)
        else {
            return
        }

        handleCustomURL(url)
    }

    private func handleCustomURL(_ url: URL) {
        guard url.scheme == "augment" else { return }

        switch url.host {
        case "version-history":
            if let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
                let queryItems = components.queryItems,
                let filePathItem = queryItems.first(where: { $0.name == "file" }),
                let filePath = filePathItem.value
            {

                // Bring app to front
                NSApp.activate(ignoringOtherApps: true)

                // Post notification to open version history
                NotificationCenter.default.post(
                    name: NSNotification.Name("OpenVersionHistory"),
                    object: filePath
                )
            }
        default:
            break
        }
    }

    private func initializeFileSystemMonitor() {
        // TODO: REFACTOR TO DEPENDENCY INJECTION - Currently using singletons for compatibility
        let fileSystem = AugmentFileSystem.shared
        let fileSystemMonitor = FileSystemMonitor.shared
        let fileOperationInterceptor = FileOperationInterceptor.shared

        // Get all spaces
        let spaces = fileSystem.getSpaces()

        // Initialize the file system monitor for each space
        for space in spaces {
            _ = fileSystemMonitor.startMonitoring(
                spacePath: space.path
            ) { (url, event) in
                // Handle file system events
                fileOperationInterceptor.handleFileSystemEvent(
                    url: url, event: event, space: space)
            }
        }
    }

    private func registerForFileSystemEvents() {
        // Register for file system events using the FileSystemMonitor
        // This is handled in initializeFileSystemMonitor
    }

    private func initializeSearchEngine() {
        // TODO: REFACTOR TO DEPENDENCY INJECTION - Currently using singletons for compatibility
        let fileSystem = AugmentFileSystem.shared
        let searchEngine = SearchEngine.shared

        // Get all spaces
        let spaces = fileSystem.getSpaces()

        // Index each space serially to prevent race conditions
        // Use a single background queue to serialize the indexing operations
        DispatchQueue.global(qos: .background).async {
            print("SearchEngine: Starting indexing of \(spaces.count) spaces...")

            for (index, space) in spaces.enumerated() {
                print("SearchEngine: Indexing space \(index + 1)/\(spaces.count): \(space.name)")

                do {
                    let success = SearchEngine.shared.indexSpace(spacePath: space.path)
                    if success {
                        print("SearchEngine: Successfully indexed space: \(space.name)")
                    } else {
                        print("SearchEngine: Failed to index space: \(space.name)")
                    }
                } catch {
                    print("SearchEngine: Error indexing space \(space.name): \(error)")
                }
            }

            print("SearchEngine: Completed indexing all spaces")
        }
    }

    private func startSnapshotScheduler() {
        // Create a timer that runs every hour to check for scheduled snapshots
        snapshotScheduler = Timer.scheduledTimer(withTimeInterval: 60 * 60, repeats: true) {
            [weak self] _ in
            self?.checkScheduledSnapshots()
        }
    }

    private func checkScheduledSnapshots() {
        // This will be handled by the SnapshotScheduler in SnapshotManager
    }
}

struct MenuBarView: View {
    @State private var spaces: [AugmentSpace] = []
    @State private var isCreatingNewSpace = false
    @State private var newSpaceName = ""
    @State private var newSpaceLocation = ""
    @Binding var isShowingPreferences: Bool

    // TODO: REFACTOR TO DEPENDENCY INJECTION - Currently using singletons for compatibility
    private let fileSystem = AugmentFileSystem.shared

    var body: some View {
        VStack(alignment: .leading) {
            Text("Augment Spaces")
                .font(.headline)
                .padding(.bottom, 5)

            Divider()

            if spaces.isEmpty {
                Text("No spaces available")
                    .foregroundColor(.gray)
                    .padding(.vertical, 5)
            } else {
                ForEach(spaces) { space in
                    Button(space.name) {
                        openSpace(space)
                    }
                }
            }

            Divider()

            Button("Create New Space...") {
                isCreatingNewSpace = true
            }

            Button("Open Space...") {
                openSpaceDialog()
            }

            Divider()

            Button("Preferences...") {
                openPreferences()
            }

            Button("Help") {
                HelpSystem.shared.showHelp(for: .gettingStarted)
            }

            Divider()

            Button("Quit") {
                NSApplication.shared.terminate(nil)
            }
        }
        .padding()
        .frame(width: 200)
        .onAppear {
            loadSpaces()
        }
        .sheet(isPresented: $isCreatingNewSpace) {
            NewSpaceView(
                isPresented: $isCreatingNewSpace,
                spaceName: $newSpaceName,
                spaceLocation: $newSpaceLocation,
                onCreateSpace: createNewSpace
            )
        }
    }

    private func loadSpaces() {
        // Load spaces from the file system
        spaces = fileSystem.getSpaces()
    }

    private func openSpace(_ space: AugmentSpace) {
        // Open the main app window
        NSApp.activate(ignoringOtherApps: true)

        // TODO: Navigate to the space in the main window
    }

    private func openSpaceDialog() {
        // Create an open panel
        let openPanel = NSOpenPanel()
        openPanel.canChooseDirectories = true
        openPanel.canChooseFiles = false
        openPanel.allowsMultipleSelection = false
        openPanel.message = "Select a folder to open as an Augment space"

        // Show the panel
        openPanel.begin { response in
            if response == .OK, let url = openPanel.url {
                // Create a space for the selected folder
                if let space = fileSystem.createSpace(name: url.lastPathComponent, path: url) {
                    spaces.append(space)
                    openSpace(space)
                }
            }
        }
    }

    private func createNewSpace() {
        guard !newSpaceName.isEmpty, !newSpaceLocation.isEmpty else { return }

        // Create the space using the file system
        if let newSpace = fileSystem.createSpace(
            name: newSpaceName,
            path: URL(fileURLWithPath: newSpaceLocation)
        ) {
            spaces.append(newSpace)
            openSpace(newSpace)
        }

        // Reset form
        newSpaceName = ""
        newSpaceLocation = ""
        isCreatingNewSpace = false
    }

    private func openPreferences() {
        print("🚨 PREFERENCES CALLED FROM MENUBARVIEW!")
        print("🔧 DEBUG: openPreferences() called in MenuBarView")

        // Open preferences window
        NSApp.activate(ignoringOtherApps: true)

        // Try to find existing preferences window
        for window in NSApp.windows {
            print("🔧 DEBUG: Found window with title: '\(window.title)'")
            if window.title == "Augment Preferences" {
                print("🔧 DEBUG: Found existing preferences window, bringing to front")
                window.makeKeyAndOrderFront(nil)
                return
            }
        }

        print("🔧 DEBUG: Creating new preferences window from MenuBarView")

        // If no window found, create a new preferences window manually
        let preferencesWindow = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 800, height: 700),
            styleMask: [.titled, .closable, .miniaturizable, .resizable],
            backing: .buffered,
            defer: false
        )
        preferencesWindow.title = "Augment Preferences"
        preferencesWindow.isReleasedWhenClosed = false
        preferencesWindow.minSize = NSSize(width: 700, height: 600)
        preferencesWindow.maxSize = NSSize(width: 1200, height: 1000)

        print("🔧 DEBUG: Creating SimplePreferencesView from MenuBarView")

        // Create the actual Storage Management preferences view
        let preferencesView = SimplePreferencesView()

        // Create the hosting view with our preferences
        let hostingView = NSHostingView(rootView: preferencesView)
        hostingView.frame = NSRect(x: 0, y: 0, width: 800, height: 700)

        preferencesWindow.contentView = hostingView
        preferencesWindow.center()
        preferencesWindow.makeKeyAndOrderFront(nil)

        print("🔧 DEBUG: Preferences window created and displayed from MenuBarView")

        // Note: Delegate removed to prevent deallocation warning
        // preferencesWindow.delegate = PreferencesWindowDelegate()
    }
}

// MARK: - Preferences Window Delegate

class PreferencesWindowDelegate: NSObject, NSWindowDelegate {
    func windowWillClose(_ notification: Notification) {
        // Handle window closing if needed
    }
}

// MARK: - Simple Preferences View

struct SimplePreferencesView: View {
    @State private var selectedTab = 0
    @ObservedObject private var preferencesManager = PreferencesManager.shared

    var body: some View {
        VStack(spacing: 0) {
            // Title bar
            HStack {
                Text("Augment Preferences")
                    .font(.title2)
                    .fontWeight(.semibold)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            .padding(.bottom, 10)

            TabView(selection: $selectedTab) {
                // General tab
                VStack(alignment: .leading, spacing: 25) {
                    Text("General")
                        .font(.title)
                        .fontWeight(.semibold)
                        .padding(.bottom, 15)

                    GroupBox(label: Text("Version Control")) {
                        VStack(alignment: .leading, spacing: 10) {
                            Toggle(
                                "Automatically version files when changed",
                                isOn: $preferencesManager.autoVersioningEnabled
                            )
                            .padding(.vertical, 5)

                            Text(
                                "When enabled, Augment will automatically create a new version each time a file is modified."
                            )
                            .font(.caption)
                            .foregroundColor(.gray)
                        }
                        .padding(10)
                    }

                    Spacer()
                }
                .padding(20)
                .tabItem {
                    Label("General", systemImage: "gear")
                }
                .tag(0)

                // Storage tab
                VStack(alignment: .leading, spacing: 25) {
                    Text("Storage Management")
                        .font(.title)
                        .fontWeight(.semibold)
                        .padding(.bottom, 15)

                    GroupBox(label: Text("Storage Limits")) {
                        VStack(alignment: .leading, spacing: 15) {
                            Toggle(
                                "Enable storage management",
                                isOn: $preferencesManager.storageManagementEnabled
                            )
                            .padding(.vertical, 5)

                            if preferencesManager.storageManagementEnabled {
                                VStack(alignment: .leading, spacing: 10) {
                                    HStack {
                                        Text("Maximum storage per space:")
                                        Spacer()
                                        TextField(
                                            "GB", value: $preferencesManager.maxStorageGB,
                                            format: .number
                                        )
                                        .textFieldStyle(RoundedBorderTextFieldStyle())
                                        .frame(width: 80)
                                        Text("GB")
                                    }

                                    HStack {
                                        Text("Warning threshold:")
                                        Spacer()
                                        TextField(
                                            "%", value: $preferencesManager.storageWarningThreshold,
                                            format: .number
                                        )
                                        .textFieldStyle(RoundedBorderTextFieldStyle())
                                        .frame(width: 80)
                                        Text("%")
                                    }

                                    Text(
                                        "Augment will warn you when storage usage exceeds this percentage."
                                    )
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                }
                                .padding(.leading, 20)
                            }
                        }
                        .padding(10)
                    }

                    GroupBox(label: Text("Automatic Cleanup")) {
                        VStack(alignment: .leading, spacing: 15) {
                            Toggle(
                                "Enable automatic cleanup",
                                isOn: $preferencesManager.autoCleanupEnabled
                            )
                            .padding(.vertical, 5)

                            if preferencesManager.autoCleanupEnabled {
                                VStack(alignment: .leading, spacing: 10) {
                                    HStack {
                                        Text("Remove versions older than:")
                                        Spacer()
                                        TextField(
                                            "Days", value: $preferencesManager.maxVersionAgeDays,
                                            format: .number
                                        )
                                        .textFieldStyle(RoundedBorderTextFieldStyle())
                                        .frame(width: 80)
                                        Text("days")
                                    }

                                    HStack {
                                        Text("Cleanup frequency:")
                                        Spacer()
                                        TextField(
                                            "Hours",
                                            value: $preferencesManager.cleanupFrequencyHours,
                                            format: .number
                                        )
                                        .textFieldStyle(RoundedBorderTextFieldStyle())
                                        .frame(width: 80)
                                        Text("hours")
                                    }

                                    Text(
                                        "Automatic cleanup runs in the background to maintain storage limits."
                                    )
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                }
                                .padding(.leading, 20)
                            }
                        }
                        .padding(10)
                    }

                    GroupBox(label: Text("Notifications")) {
                        VStack(alignment: .leading, spacing: 10) {
                            Toggle(
                                "Show storage notifications",
                                isOn: $preferencesManager.storageNotificationsEnabled
                            )
                            .padding(.vertical, 5)

                            Text(
                                "Receive notifications when storage limits are approached or exceeded."
                            )
                            .font(.caption)
                            .foregroundColor(.gray)
                        }
                        .padding(10)
                    }

                    Spacer()
                }
                .padding(20)
                .tabItem {
                    Label("Storage", systemImage: "internaldrive")
                }
                .tag(1)
            }
            .tabViewStyle(DefaultTabViewStyle())
        }
        .frame(width: 800, height: 700)
        .background(Color(NSColor.windowBackgroundColor))
    }
}
