import XCTest
@testable import AugmentCore

/// Comprehensive tests for onboarding and error recovery systems
class OnboardingAndErrorRecoveryTests: XCTestCase {
    
    var helpSystem: HelpSystem!
    var errorRecoveryManager: ErrorRecoveryManager!
    
    override func setUp() {
        super.setUp()
        helpSystem = HelpSystem.shared
        errorRecoveryManager = ErrorRecoveryManager.shared
    }
    
    override func tearDown() {
        // Clean up any test state
        helpSystem.hideHelp()
        errorRecoveryManager.dismissErrorDialog()
        super.tearDown()
    }
    
    // MARK: - Help System Tests
    
    func testHelpSystemInitialization() {
        XCTAssertNotNil(helpSystem, "Help system should be initialized")
        XCTAssertFalse(helpSystem.isShowingHelp, "Help should not be showing initially")
        XCTAssertNil(helpSystem.currentHelpTopic, "No help topic should be selected initially")
    }
    
    func testShowHelp() {
        // Test showing help for a specific topic
        helpSystem.showHelp(for: .gettingStarted)
        
        XCTAssertTrue(helpSystem.isShowingHelp, "Help should be showing")
        XCTAssertEqual(helpSystem.currentHelpTopic, .gettingStarted, "Correct help topic should be selected")
    }
    
    func testHelpSearch() {
        // Test searching help articles
        helpSystem.searchHelp("getting started")
        
        XCTAssertFalse(helpSystem.searchResults.isEmpty, "Search should return results")
        XCTAssertTrue(
            helpSystem.searchResults.contains { $0.title.lowercased().contains("getting started") },
            "Search results should contain relevant articles"
        )
    }
    
    func testHelpSearchEmpty() {
        // Test empty search
        helpSystem.searchHelp("")
        
        XCTAssertTrue(helpSystem.searchResults.isEmpty, "Empty search should return no results")
    }
    
    func testHelpCategories() {
        // Test getting articles by category
        let gettingStartedArticles = helpSystem.getArticles(for: .gettingStarted)
        
        XCTAssertFalse(gettingStartedArticles.isEmpty, "Should have getting started articles")
        XCTAssertTrue(
            gettingStartedArticles.allSatisfy { $0.category == .gettingStarted },
            "All articles should be in the correct category"
        )
    }
    
    func testContextualHelp() {
        // Test contextual help
        helpSystem.showContextualHelp(for: "preferences")
        
        XCTAssertTrue(helpSystem.isShowingHelp, "Help should be showing")
        if case .contextualHelp(let context) = helpSystem.currentHelpTopic {
            XCTAssertEqual(context, "preferences", "Correct context should be set")
        } else {
            XCTFail("Should show contextual help")
        }
    }
    
    // MARK: - Error Recovery Tests
    
    func testErrorRecoveryInitialization() {
        XCTAssertNotNil(errorRecoveryManager, "Error recovery manager should be initialized")
        XCTAssertTrue(errorRecoveryManager.activeErrors.isEmpty, "No active errors initially")
        XCTAssertFalse(errorRecoveryManager.isShowingErrorDialog, "Error dialog should not be showing")
    }
    
    func testErrorHandling() {
        // Create a test error
        let testError = NSError(domain: "TestDomain", code: 1001, userInfo: [
            NSLocalizedDescriptionKey: "Test error for unit testing"
        ])
        
        // Handle the error without auto-recovery
        errorRecoveryManager.handleError(testError, context: "Unit test", autoRecover: false)
        
        // Verify error was recorded
        XCTAssertFalse(errorRecoveryManager.activeErrors.isEmpty, "Error should be recorded")
        XCTAssertTrue(errorRecoveryManager.isShowingErrorDialog, "Error dialog should be showing")
        
        let recordedError = errorRecoveryManager.activeErrors.first!
        XCTAssertEqual(recordedError.originalError.localizedDescription, testError.localizedDescription)
        XCTAssertEqual(recordedError.context, "Unit test")
    }
    
    func testErrorCategorization() {
        // Test different error types are categorized correctly
        let fileSystemError = FileSystemError.fileNotFound("/test/path")
        let storageError = StorageError.insufficientSpace(available: 100, required: 200)
        let permissionError = PermissionError.accessDenied("/test/path")
        
        errorRecoveryManager.handleError(fileSystemError, autoRecover: false)
        errorRecoveryManager.handleError(storageError, autoRecover: false)
        errorRecoveryManager.handleError(permissionError, autoRecover: false)
        
        let errors = errorRecoveryManager.activeErrors
        XCTAssertEqual(errors.count, 3, "Should have 3 errors")
        
        // Check categorization
        let categories = errors.map { $0.category }
        XCTAssertTrue(categories.contains(.fileSystem), "Should categorize file system error")
        XCTAssertTrue(categories.contains(.storage), "Should categorize storage error")
        XCTAssertTrue(categories.contains(.permissions), "Should categorize permission error")
    }
    
    func testRecoveryStrategies() {
        // Test that appropriate recovery strategies are assigned
        let storageError = StorageError.insufficientSpace(available: 100, required: 200)
        errorRecoveryManager.handleError(storageError, autoRecover: false)
        
        let recordedError = errorRecoveryManager.activeErrors.first!
        let strategies = recordedError.recoveryStrategies
        
        XCTAssertTrue(strategies.contains(.freeUpSpace), "Storage error should suggest freeing up space")
        XCTAssertTrue(strategies.contains(.runCleanup), "Storage error should suggest running cleanup")
        XCTAssertTrue(strategies.contains(.adjustStorageSettings), "Storage error should suggest adjusting settings")
    }
    
    func testAutomaticRecovery() {
        // Test automatic recovery attempt
        let expectation = XCTestExpectation(description: "Automatic recovery attempted")
        
        let testError = NSError(domain: "TestDomain", code: 1001, userInfo: nil)
        
        // Handle error with auto-recovery enabled
        errorRecoveryManager.handleError(testError, autoRecover: true)
        
        // Give some time for automatic recovery to be attempted
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 2.0)
        
        // Verify recovery was attempted (error might be resolved or dialog shown)
        let hasActiveErrors = !errorRecoveryManager.activeErrors.isEmpty
        let isShowingDialog = errorRecoveryManager.isShowingErrorDialog
        
        XCTAssertTrue(hasActiveErrors || isShowingDialog, "Recovery should have been attempted")
    }
    
    func testErrorHistory() {
        // Test error history tracking
        let testError1 = NSError(domain: "TestDomain", code: 1001, userInfo: nil)
        let testError2 = NSError(domain: "TestDomain", code: 1002, userInfo: nil)
        
        errorRecoveryManager.handleError(testError1, autoRecover: false)
        errorRecoveryManager.handleError(testError2, autoRecover: false)
        
        let history = errorRecoveryManager.getErrorHistory()
        XCTAssertGreaterThanOrEqual(history.count, 2, "Should have at least 2 errors in history")
        
        // Check that most recent error is first
        XCTAssertTrue(history[0].occurredAt >= history[1].occurredAt, "History should be sorted by date")
    }
    
    func testErrorDismissal() {
        // Test dismissing error dialog
        let testError = NSError(domain: "TestDomain", code: 1001, userInfo: nil)
        errorRecoveryManager.handleError(testError, autoRecover: false)
        
        XCTAssertTrue(errorRecoveryManager.isShowingErrorDialog, "Error dialog should be showing")
        
        errorRecoveryManager.dismissErrorDialog()
        
        XCTAssertFalse(errorRecoveryManager.isShowingErrorDialog, "Error dialog should be dismissed")
        XCTAssertNil(errorRecoveryManager.currentError, "Current error should be cleared")
    }
    
    // MARK: - Integration Tests
    
    func testOnboardingIntegration() {
        // Test that onboarding state is properly managed
        let hasCompletedOnboarding = UserDefaults.standard.bool(forKey: "hasCompletedOnboarding")
        
        // If onboarding hasn't been completed, it should be shown on first launch
        if !hasCompletedOnboarding {
            // This would be tested in UI tests, but we can verify the UserDefaults key exists
            XCTAssertFalse(hasCompletedOnboarding, "Onboarding should not be completed for new users")
        }
    }
    
    func testHelpSystemErrorRecoveryIntegration() {
        // Test that error recovery can trigger help system
        let testError = NSError(domain: "TestDomain", code: 1001, userInfo: nil)
        errorRecoveryManager.handleError(testError, autoRecover: false)
        
        // Simulate user clicking "Get Help" in error dialog
        helpSystem.showHelp(for: .troubleshooting)
        
        XCTAssertTrue(helpSystem.isShowingHelp, "Help should be showing")
        XCTAssertEqual(helpSystem.currentHelpTopic, .troubleshooting, "Should show troubleshooting help")
    }
    
    // MARK: - Performance Tests
    
    func testHelpSearchPerformance() {
        // Test that help search is performant
        measure {
            for _ in 0..<100 {
                helpSystem.searchHelp("test query")
            }
        }
    }
    
    func testErrorHandlingPerformance() {
        // Test that error handling is performant
        measure {
            for i in 0..<50 {
                let testError = NSError(domain: "TestDomain", code: i, userInfo: nil)
                errorRecoveryManager.handleError(testError, autoRecover: false)
            }
        }
    }
    
    // MARK: - Edge Cases
    
    func testMultipleSimultaneousErrors() {
        // Test handling multiple errors at once
        let errors = (1...5).map { i in
            NSError(domain: "TestDomain", code: i, userInfo: [
                NSLocalizedDescriptionKey: "Test error \(i)"
            ])
        }
        
        for error in errors {
            errorRecoveryManager.handleError(error, autoRecover: false)
        }
        
        XCTAssertEqual(errorRecoveryManager.activeErrors.count, 5, "Should handle multiple errors")
    }
    
    func testErrorRecoveryWithNilContext() {
        // Test error handling with nil context
        let testError = NSError(domain: "TestDomain", code: 1001, userInfo: nil)
        errorRecoveryManager.handleError(testError, context: nil, autoRecover: false)
        
        let recordedError = errorRecoveryManager.activeErrors.first!
        XCTAssertNil(recordedError.context, "Context should be nil")
        XCTAssertFalse(recordedError.userMessage.isEmpty, "User message should still be generated")
    }
    
    func testHelpSearchWithSpecialCharacters() {
        // Test help search with special characters
        helpSystem.searchHelp("test@#$%^&*()")
        
        // Should not crash and should return empty results
        XCTAssertTrue(helpSystem.searchResults.isEmpty, "Special character search should return no results")
    }
}
