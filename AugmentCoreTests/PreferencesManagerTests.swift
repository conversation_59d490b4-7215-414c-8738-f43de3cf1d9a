import XCTest
@testable import AugmentCore

/// Unit tests for PreferencesManager
/// Tests settings persistence, backend integration, and preference application
class PreferencesManagerTests: XCTestCase {
    
    var preferencesManager: PreferencesManager!
    var testUserDefaults: UserDefaults!
    
    override func setUp() {
        super.setUp()
        
        // Create a test UserDefaults suite to avoid affecting real preferences
        testUserDefaults = UserDefaults(suiteName: "PreferencesManagerTests")!
        
        // Clear any existing test data
        testUserDefaults.removePersistentDomain(forName: "PreferencesManagerTests")
        
        // Note: For full testing, we'd need to inject the test UserDefaults
        // For now, we'll test the shared instance with awareness of side effects
        preferencesManager = PreferencesManager.shared
    }
    
    override func tearDown() {
        // Clean up test data
        testUserDefaults.removePersistentDomain(forName: "PreferencesManagerTests")
        super.tearDown()
    }
    
    // MARK: - Settings Persistence Tests
    
    func testAutoVersioningPersistence() {
        // Test that auto-versioning setting persists
        let originalValue = preferencesManager.autoVersioningEnabled
        
        // Change the value
        preferencesManager.autoVersioningEnabled = !originalValue
        
        // Verify it was saved to UserDefaults
        let savedValue = UserDefaults.standard.bool(forKey: "autoVersioningEnabled")
        XCTAssertEqual(savedValue, !originalValue, "Auto-versioning setting should persist to UserDefaults")
        
        // Restore original value
        preferencesManager.autoVersioningEnabled = originalValue
    }
    
    func testStorageManagementPersistence() {
        // Test that storage management settings persist
        let originalEnabled = preferencesManager.storageManagementEnabled
        let originalMaxGB = preferencesManager.maxStorageGB
        let originalThreshold = preferencesManager.storageWarningThreshold
        
        // Change the values
        preferencesManager.storageManagementEnabled = !originalEnabled
        preferencesManager.maxStorageGB = 25.0
        preferencesManager.storageWarningThreshold = 90.0
        
        // Verify they were saved to UserDefaults
        XCTAssertEqual(
            UserDefaults.standard.bool(forKey: "storageManagementEnabled"),
            !originalEnabled,
            "Storage management enabled setting should persist"
        )
        XCTAssertEqual(
            UserDefaults.standard.double(forKey: "maxStorageGB"),
            25.0,
            "Max storage GB setting should persist"
        )
        XCTAssertEqual(
            UserDefaults.standard.double(forKey: "storageWarningThreshold"),
            90.0,
            "Storage warning threshold setting should persist"
        )
        
        // Restore original values
        preferencesManager.storageManagementEnabled = originalEnabled
        preferencesManager.maxStorageGB = originalMaxGB
        preferencesManager.storageWarningThreshold = originalThreshold
    }
    
    func testCleanupSettingsPersistence() {
        // Test that cleanup settings persist
        let originalEnabled = preferencesManager.autoCleanupEnabled
        let originalFrequency = preferencesManager.cleanupFrequencyHours
        let originalMaxAge = preferencesManager.maxVersionAgeDays
        
        // Change the values
        preferencesManager.autoCleanupEnabled = !originalEnabled
        preferencesManager.cleanupFrequencyHours = 48
        preferencesManager.maxVersionAgeDays = 180
        
        // Verify they were saved to UserDefaults
        XCTAssertEqual(
            UserDefaults.standard.bool(forKey: "autoCleanupEnabled"),
            !originalEnabled,
            "Auto cleanup enabled setting should persist"
        )
        XCTAssertEqual(
            UserDefaults.standard.integer(forKey: "cleanupFrequencyHours"),
            48,
            "Cleanup frequency setting should persist"
        )
        XCTAssertEqual(
            UserDefaults.standard.integer(forKey: "maxVersionAgeDays"),
            180,
            "Max version age setting should persist"
        )
        
        // Restore original values
        preferencesManager.autoCleanupEnabled = originalEnabled
        preferencesManager.cleanupFrequencyHours = originalFrequency
        preferencesManager.maxVersionAgeDays = originalMaxAge
    }
    
    func testNotificationSettingsPersistence() {
        // Test that notification settings persist
        let originalValue = preferencesManager.storageNotificationsEnabled
        
        // Change the value
        preferencesManager.storageNotificationsEnabled = !originalValue
        
        // Verify it was saved to UserDefaults
        let savedValue = UserDefaults.standard.bool(forKey: "storageNotificationsEnabled")
        XCTAssertEqual(savedValue, !originalValue, "Storage notifications setting should persist to UserDefaults")
        
        // Restore original value
        preferencesManager.storageNotificationsEnabled = originalValue
    }
    
    // MARK: - Default Values Tests
    
    func testDefaultValues() {
        // Test that default values are reasonable
        XCTAssertTrue(preferencesManager.autoVersioningEnabled, "Auto-versioning should be enabled by default")
        XCTAssertTrue(preferencesManager.storageManagementEnabled, "Storage management should be enabled by default")
        XCTAssertGreaterThan(preferencesManager.maxStorageGB, 0, "Max storage should be greater than 0")
        XCTAssertGreaterThan(preferencesManager.storageWarningThreshold, 0, "Warning threshold should be greater than 0")
        XCTAssertLessThanOrEqual(preferencesManager.storageWarningThreshold, 100, "Warning threshold should be <= 100")
        XCTAssertTrue(preferencesManager.autoCleanupEnabled, "Auto cleanup should be enabled by default")
        XCTAssertGreaterThan(preferencesManager.cleanupFrequencyHours, 0, "Cleanup frequency should be greater than 0")
        XCTAssertGreaterThan(preferencesManager.maxVersionAgeDays, 0, "Max version age should be greater than 0")
        XCTAssertTrue(preferencesManager.storageNotificationsEnabled, "Storage notifications should be enabled by default")
    }
    
    // MARK: - Export/Import Tests
    
    func testExportImportPreferences() {
        // Set some test values
        preferencesManager.autoVersioningEnabled = false
        preferencesManager.maxStorageGB = 15.0
        preferencesManager.storageWarningThreshold = 85.0
        preferencesManager.autoCleanupEnabled = false
        preferencesManager.cleanupFrequencyHours = 12
        preferencesManager.maxVersionAgeDays = 180
        preferencesManager.storageNotificationsEnabled = false
        
        // Export preferences
        let exported = preferencesManager.exportPreferences()
        
        // Verify exported values
        XCTAssertEqual(exported["autoVersioningEnabled"] as? Bool, false)
        XCTAssertEqual(exported["maxStorageGB"] as? Double, 15.0)
        XCTAssertEqual(exported["storageWarningThreshold"] as? Double, 85.0)
        XCTAssertEqual(exported["autoCleanupEnabled"] as? Bool, false)
        XCTAssertEqual(exported["cleanupFrequencyHours"] as? Int, 12)
        XCTAssertEqual(exported["maxVersionAgeDays"] as? Int, 180)
        XCTAssertEqual(exported["storageNotificationsEnabled"] as? Bool, false)
        
        // Change values
        preferencesManager.autoVersioningEnabled = true
        preferencesManager.maxStorageGB = 20.0
        
        // Import the exported preferences
        preferencesManager.importPreferences(exported)
        
        // Verify values were restored
        XCTAssertEqual(preferencesManager.autoVersioningEnabled, false)
        XCTAssertEqual(preferencesManager.maxStorageGB, 15.0)
        XCTAssertEqual(preferencesManager.storageWarningThreshold, 85.0)
        XCTAssertEqual(preferencesManager.autoCleanupEnabled, false)
        XCTAssertEqual(preferencesManager.cleanupFrequencyHours, 12)
        XCTAssertEqual(preferencesManager.maxVersionAgeDays, 180)
        XCTAssertEqual(preferencesManager.storageNotificationsEnabled, false)
    }
    
    // MARK: - Reset to Defaults Tests
    
    func testResetToDefaults() {
        // Change all values from defaults
        preferencesManager.autoVersioningEnabled = false
        preferencesManager.storageManagementEnabled = false
        preferencesManager.maxStorageGB = 5.0
        preferencesManager.storageWarningThreshold = 50.0
        preferencesManager.autoCleanupEnabled = false
        preferencesManager.cleanupFrequencyHours = 12
        preferencesManager.maxVersionAgeDays = 30
        preferencesManager.storageNotificationsEnabled = false
        
        // Reset to defaults
        preferencesManager.resetToDefaults()
        
        // Verify all values are back to defaults
        XCTAssertTrue(preferencesManager.autoVersioningEnabled)
        XCTAssertTrue(preferencesManager.storageManagementEnabled)
        XCTAssertEqual(preferencesManager.maxStorageGB, 10.0)
        XCTAssertEqual(preferencesManager.storageWarningThreshold, 80.0)
        XCTAssertTrue(preferencesManager.autoCleanupEnabled)
        XCTAssertEqual(preferencesManager.cleanupFrequencyHours, 24)
        XCTAssertEqual(preferencesManager.maxVersionAgeDays, 365)
        XCTAssertTrue(preferencesManager.storageNotificationsEnabled)
    }
    
    // MARK: - Performance Tests
    
    func testSettingsPerformance() {
        // Test that settings changes are performant
        measure {
            for _ in 0..<100 {
                preferencesManager.maxStorageGB = Double.random(in: 1.0...100.0)
                preferencesManager.storageWarningThreshold = Double.random(in: 50.0...95.0)
                preferencesManager.cleanupFrequencyHours = Int.random(in: 1...48)
                preferencesManager.maxVersionAgeDays = Int.random(in: 30...730)
            }
        }
    }
}

// MARK: - Integration Tests

extension PreferencesManagerTests {
    
    func testStorageManagerIntegration() {
        // Test that preferences changes affect the storage manager
        let storageManager = StorageManager.shared
        
        // Enable storage management
        preferencesManager.storageManagementEnabled = true
        
        // This should trigger the storage manager to be enabled
        // Note: In a real test, we'd verify this through dependency injection
        // For now, we just verify the setting was applied
        XCTAssertTrue(preferencesManager.storageManagementEnabled)
    }
    
    func testNotificationManagerIntegration() {
        // Test that notification preferences affect the notification manager
        let notificationManager = NotificationManager.shared
        
        // Enable notifications
        preferencesManager.storageNotificationsEnabled = true
        
        // This should trigger the notification manager to enable storage notifications
        // Note: In a real test, we'd verify this through dependency injection
        XCTAssertTrue(preferencesManager.storageNotificationsEnabled)
    }
}
